/*M!999999\- enable the sandbox mode */ 
-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.11.10-MariaDB, for Linux (x86_64)
--
-- Host: localhost    Database: u199973385_zoom
-- ------------------------------------------------------
-- Server version	10.11.10-MariaDB-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admin_tokens`
--

DROP TABLE IF EXISTS `admin_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` varchar(191) NOT NULL,
  `fcm_token` varchar(255) NOT NULL,
  `platform` varchar(191) NOT NULL DEFAULT 'web',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_tokens`
--

LOCK TABLES `admin_tokens` WRITE;
/*!40000 ALTER TABLE `admin_tokens` DISABLE KEYS */;
INSERT INTO `admin_tokens` VALUES
(1,1,'Super Admin','d3yOhFXspWJEomQY4alI1M:APA91bHG6AoLXHANreKg4QKWrsrOdfdp-uJP6LQn2fE7I3MX7lXhhdLaiKwlit0qeyBFjdTav53k3UycvUL4-V20xpiwXbsngErtjX-ZkAbNBV-_I2iYbXg','web'),
(2,1,'Super Admin','cr10AD3a3kBggDX-wovp9q:APA91bHwQIxUZn-NjFF0bLJixTRdxKeDPRXoQkLNv4mDhUvTjxUvxlqOxitM6ETHf4F5hW3oXYt37hrEXMHVk8x6vy_VvQ_cORdKVxzBZT3tP-PsZu1ujJk','web'),
(3,2,'Seller','cr10AD3a3kBggDX-wovp9q:APA91bHwQIxUZn-NjFF0bLJixTRdxKeDPRXoQkLNv4mDhUvTjxUvxlqOxitM6ETHf4F5hW3oXYt37hrEXMHVk8x6vy_VvQ_cORdKVxzBZT3tP-PsZu1ujJk','web');
/*!40000 ALTER TABLE `admin_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admins`
--

DROP TABLE IF EXISTS `admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admins` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(191) NOT NULL,
  `email` varchar(191) NOT NULL,
  `password` text NOT NULL,
  `role_id` int(11) NOT NULL,
  `created_by` int(11) NOT NULL,
  `forgot_password_code` varchar(191) DEFAULT NULL,
  `fcm_id` varchar(191) DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1 => Active, 0 => Inactive',
  `login_at` timestamp NULL DEFAULT NULL,
  `last_active_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admins`
--

LOCK TABLES `admins` WRITE;
/*!40000 ALTER TABLE `admins` DISABLE KEYS */;
INSERT INTO `admins` VALUES
(1,'superadmin','<EMAIL>','$2y$10$iGOxgyXxk69yi5kXcwsTeOQi.wrBBLq18A0XaousjStPGqtTppLn.',1,1,NULL,NULL,NULL,1,NULL,NULL,'2025-06-20 14:17:34','2025-06-20 14:17:34'),
(2,'Zoomfresh','<EMAIL>','$2y$10$IVqc8UR2N26XMqTFZd2iou2lSRPcOfzn2jCs73F7u3a.2yNolu2/y',3,0,NULL,NULL,NULL,1,NULL,NULL,'2025-06-20 14:35:25','2025-07-29 18:57:11');
/*!40000 ALTER TABLE `admins` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `app_usages`
--

DROP TABLE IF EXISTS `app_usages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `app_usages` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` varchar(191) NOT NULL,
  `device_type` varchar(191) NOT NULL,
  `app_version` varchar(191) NOT NULL,
  `created_at` timestamp NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `app_usages`
--

LOCK TABLES `app_usages` WRITE;
/*!40000 ALTER TABLE `app_usages` DISABLE KEYS */;
INSERT INTO `app_usages` VALUES
(1,'8','Android','2.0.10','2025-06-27 19:45:02');
/*!40000 ALTER TABLE `app_usages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `area`
--

DROP TABLE IF EXISTS `area`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `area` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `city_id` int(11) DEFAULT 0,
  `pincode_id` int(11) DEFAULT NULL,
  `name` text NOT NULL,
  `minimum_free_delivery_order_amount` int(11) NOT NULL DEFAULT 0,
  `delivery_charges` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `area`
--

LOCK TABLES `area` WRITE;
/*!40000 ALTER TABLE `area` DISABLE KEYS */;
/*!40000 ALTER TABLE `area` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `brands`
--

DROP TABLE IF EXISTS `brands`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `brands` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `image` text NOT NULL,
  `status` varchar(191) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `brands`
--

LOCK TABLES `brands` WRITE;
/*!40000 ALTER TABLE `brands` DISABLE KEYS */;
/*!40000 ALTER TABLE `brands` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cart_notifications`
--

DROP TABLE IF EXISTS `cart_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cart_notifications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `cart_id` bigint(20) unsigned NOT NULL,
  `title` varchar(191) NOT NULL,
  `message` text NOT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cart_notifications_user_id_foreign` (`user_id`),
  KEY `cart_notifications_cart_id_foreign` (`cart_id`),
  CONSTRAINT `cart_notifications_cart_id_foreign` FOREIGN KEY (`cart_id`) REFERENCES `carts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `cart_notifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cart_notifications`
--

LOCK TABLES `cart_notifications` WRITE;
/*!40000 ALTER TABLE `cart_notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `cart_notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `carts`
--

DROP TABLE IF EXISTS `carts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `carts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_variant_id` int(11) NOT NULL,
  `qty` decimal(8,2) NOT NULL,
  `cut_selection` varchar(255) DEFAULT NULL,
  `save_for_later` tinyint(4) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=238 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `carts`
--

LOCK TABLES `carts` WRITE;
/*!40000 ALTER TABLE `carts` DISABLE KEYS */;
INSERT INTO `carts` VALUES
(156,3,1,1,1.00,'Curry Cut',0,'2025-07-21 19:46:32',NULL),
(183,14,1,1,1.50,'Curry Cut',0,'2025-07-23 18:35:17','2025-07-23 18:37:27'),
(237,13,29,41,0.50,'Fry Cut',0,'2025-08-01 07:45:14',NULL);
/*!40000 ALTER TABLE `carts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `categories`
--

DROP TABLE IF EXISTS `categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `row_order` int(11) NOT NULL DEFAULT 0,
  `name` varchar(191) NOT NULL DEFAULT '0',
  `slug` varchar(191) DEFAULT NULL,
  `subtitle` text NOT NULL,
  `image` text NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1,
  `product_rating` tinyint(4) NOT NULL DEFAULT 0,
  `web_image` text DEFAULT NULL,
  `parent_id` int(11) NOT NULL DEFAULT 0 COMMENT '0: Main Category, Other Sub category of id',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `categories`
--

LOCK TABLES `categories` WRITE;
/*!40000 ALTER TABLE `categories` DISABLE KEYS */;
INSERT INTO `categories` VALUES
(1,0,'Freshfish','freshfish','fish','categories/1751123683_41378.jpg',1,0,'',0,'2025-06-20 14:33:24','2025-06-28 20:44:43'),
(2,0,'Chicken','chicken','Chicken','categories/1751123665_72691.jpg',1,0,'',0,'2025-06-27 16:46:28','2025-06-28 20:44:25'),
(3,0,'Mutton','mutton','Mutton','categories/1751023214_74497.jpg',1,0,'',0,'2025-06-27 16:50:14','2025-06-27 16:50:14'),
(4,0,'Beef','beef','Fresh Beef','categories/1753544015_67607.jpg',1,0,'',0,'2025-06-28 20:29:55','2025-07-26 21:03:35'),
(5,0,'Pickle','pickle','Pickle','categories/1751122881_69913.jpg',1,0,'',0,'2025-06-28 20:31:21','2025-06-28 20:31:21'),
(7,0,'Ready to cook','ready-to-cook','Ready to cook','categories/1753599388_12749.webp',1,0,'',0,'2025-07-27 12:19:46','2025-07-27 12:26:28'),
(8,0,'Freshly frozen','freshly-frozen','Freshly Frozen','categories/1753599960_4468.png',1,0,'',0,'2025-07-27 12:21:04','2025-07-27 12:36:00'),
(9,0,'Spices and Masala','spices-and-masala','Spices And Masala','categories/1753599136_63184.jpg',1,0,'',0,'2025-07-27 12:22:16','2025-07-27 12:22:16'),
(10,0,'Fresh Oils','fresh-oils','Oils','categories/1753600118_97987.jpg',1,0,'',0,'2025-07-27 12:38:38','2025-07-27 12:38:38'),
(11,0,'Dry fish','dry-fish','Dry  Fish','categories/1753600218_18837.jpg',1,0,'',0,'2025-07-27 12:40:18','2025-07-27 12:40:18'),
(12,0,'Honey','honey','Honey','categories/1753600336_30445.jpg',1,0,'',0,'2025-07-27 12:42:16','2025-07-27 12:42:16'),
(14,0,'Snacks','snacks','Snacks','categories/1753781362_9254.jpg',1,0,'',0,'2025-07-29 14:59:22','2025-07-29 14:59:22');
/*!40000 ALTER TABLE `categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cities`
--

DROP TABLE IF EXISTS `cities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `zone` varchar(191) NOT NULL,
  `state` varchar(191) NOT NULL,
  `formatted_address` varchar(191) NOT NULL,
  `latitude` varchar(191) DEFAULT NULL,
  `longitude` varchar(191) DEFAULT NULL,
  `min_amount_for_free_delivery` varchar(191) DEFAULT NULL,
  `delivery_charge_method` varchar(191) DEFAULT NULL,
  `fixed_charge` decimal(11,2) NOT NULL DEFAULT 0.00,
  `per_km_charge` decimal(11,2) NOT NULL DEFAULT 0.00,
  `range_wise_charges` text DEFAULT NULL,
  `time_to_travel` int(11) NOT NULL DEFAULT 0,
  `geolocation_type` varchar(191) DEFAULT NULL,
  `radius` varchar(191) DEFAULT '0',
  `boundary_points` text DEFAULT NULL,
  `max_deliverable_distance` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cities`
--

LOCK TABLES `cities` WRITE;
/*!40000 ALTER TABLE `cities` DISABLE KEYS */;
INSERT INTO `cities` VALUES
(1,'Nilambur','nilambur','Kerala','Nilambur, Kerala, India','11.2855356','76.2385793','499','fixed_charge',30.00,0.00,'[{\"from_range\":\"\",\"to_range\":\"\",\"price\":\"\"}]',10,'polygon','undefined','[{\"lat\":11.303531863518245,\"lng\":76.************},{\"lat\":11.28885348260283,\"lng\":76.26104873554503},{\"lat\":11.271850618754382,\"lng\":76.2431959523419},{\"lat\":11.290031861605538,\"lng\":76.2205366505841}]',0);
/*!40000 ALTER TABLE `cities` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `countries`
--

DROP TABLE IF EXISTS `countries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `countries` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `dial_code` varchar(191) NOT NULL,
  `code` varchar(191) NOT NULL,
  `logo` text DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=243 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `countries`
--

LOCK TABLES `countries` WRITE;
/*!40000 ALTER TABLE `countries` DISABLE KEYS */;
INSERT INTO `countries` VALUES
(1,'Afghanistan','+93','AF',NULL,1),
(2,'Aland Islands','+358','AX',NULL,1),
(3,'Albania','+355','AL',NULL,1),
(4,'Algeria','+213','DZ',NULL,1),
(5,'AmericanSamoa','+1684','AS',NULL,1),
(6,'Andorra','+376','AD',NULL,1),
(7,'Angola','+244','AO',NULL,1),
(8,'Anguilla','+1264','AI',NULL,1),
(9,'Antarctica','+672','AQ',NULL,1),
(10,'Antigua and Barbuda','+1268','AG',NULL,1),
(11,'Argentina','+54','AR',NULL,1),
(12,'Armenia','+374','AM',NULL,1),
(13,'Aruba','+297','AW',NULL,1),
(14,'Australia','+61','AU',NULL,1),
(15,'Austria','+43','AT',NULL,1),
(16,'Azerbaijan','+994','AZ',NULL,1),
(17,'Bahamas','+1242','BS',NULL,1),
(18,'Bahrain','+973','BH',NULL,1),
(19,'Bangladesh','+880','BD',NULL,1),
(20,'Barbados','+1246','BB',NULL,1),
(21,'Belarus','+375','BY',NULL,1),
(22,'Belgium','+32','BE',NULL,1),
(23,'Belize','+501','BZ',NULL,1),
(24,'Benin','+229','BJ',NULL,1),
(25,'Bermuda','+1441','BM',NULL,1),
(26,'Bhutan','+975','BT',NULL,1),
(27,'Bolivia, Plurinational State of','+591','BO',NULL,1),
(28,'Bosnia and Herzegovina','+387','BA',NULL,1),
(29,'Botswana','+267','BW',NULL,1),
(30,'Brazil','+55','BR',NULL,1),
(31,'British Indian Ocean Territory','+246','IO',NULL,1),
(32,'Brunei Darussalam','+673','BN',NULL,1),
(33,'Bulgaria','+359','BG',NULL,1),
(34,'Burkina Faso','+226','BF',NULL,1),
(35,'Burundi','+257','BI',NULL,1),
(36,'Cambodia','+855','KH',NULL,1),
(37,'Cameroon','+237','CM',NULL,1),
(38,'Canada','+1','CA',NULL,1),
(39,'Cape Verde','+238','CV',NULL,1),
(40,'Cayman Islands','+345','KY',NULL,1),
(41,'Central African Republic','+236','CF',NULL,1),
(42,'Chad','+235','TD',NULL,1),
(43,'Chile','+56','CL',NULL,1),
(44,'China','+86','CN',NULL,1),
(45,'Christmas Island','+61','CX',NULL,1),
(46,'Cocos (Keeling) Islands','+61','CC',NULL,1),
(47,'Colombia','+57','CO',NULL,1),
(48,'Comoros','+269','KM',NULL,1),
(49,'Congo','+242','CG',NULL,1),
(50,'Congo, The Democratic Republic of the Congo','+243','CD',NULL,1),
(51,'Cook Islands','+682','CK',NULL,1),
(52,'Costa Rica','+506','CR',NULL,1),
(53,'Cote d\'Ivoire','+225','CI',NULL,1),
(54,'Croatia','+385','HR',NULL,1),
(55,'Cuba','+53','CU',NULL,1),
(56,'Cyprus','+357','CY',NULL,1),
(57,'Czech Republic','+420','CZ',NULL,1),
(58,'Denmark','+45','DK',NULL,1),
(59,'Djibouti','+253','DJ',NULL,1),
(60,'Dominica','+1767','DM',NULL,1),
(61,'Dominican Republic','+1849','DO',NULL,1),
(62,'Ecuador','+593','EC',NULL,1),
(63,'Egypt','+20','EG',NULL,1),
(64,'El Salvador','+503','SV',NULL,1),
(65,'Equatorial Guinea','+240','GQ',NULL,1),
(66,'Eritrea','+291','ER',NULL,1),
(67,'Estonia','+372','EE',NULL,1),
(68,'Ethiopia','+251','ET',NULL,1),
(69,'Falkland Islands (Malvinas)','+500','FK',NULL,1),
(70,'Faroe Islands','+298','FO',NULL,1),
(71,'Fiji','+679','FJ',NULL,1),
(72,'Finland','+358','FI',NULL,1),
(73,'France','+33','FR',NULL,1),
(74,'French Guiana','+594','GF',NULL,1),
(75,'French Polynesia','+689','PF',NULL,1),
(76,'Gabon','+241','GA',NULL,1),
(77,'Gambia','+220','GM',NULL,1),
(78,'Georgia','+995','GE',NULL,1),
(79,'Germany','+49','DE',NULL,1),
(80,'Ghana','+233','GH',NULL,1),
(81,'Gibraltar','+350','GI',NULL,1),
(82,'Greece','+30','GR',NULL,1),
(83,'Greenland','+299','GL',NULL,1),
(84,'Grenada','+1473','GD',NULL,1),
(85,'Guadeloupe','+590','GP',NULL,1),
(86,'Guam','+1671','GU',NULL,1),
(87,'Guatemala','+502','GT',NULL,1),
(88,'Guernsey','+44','GG',NULL,1),
(89,'Guinea','+224','GN',NULL,1),
(90,'Guinea-Bissau','+245','GW',NULL,1),
(91,'Guyana','+595','GY',NULL,1),
(92,'Haiti','+509','HT',NULL,1),
(93,'Holy See (Vatican City State)','+379','VA',NULL,1),
(94,'Honduras','+504','HN',NULL,1),
(95,'Hong Kong','+852','HK',NULL,1),
(96,'Hungary','+36','HU',NULL,1),
(97,'Iceland','+354','IS',NULL,1),
(98,'India','+91','IN',NULL,1),
(99,'Indonesia','+62','ID',NULL,1),
(100,'Iran, Islamic Republic of Persian Gulf','+98','IR',NULL,1),
(101,'Iraq','+964','IQ',NULL,1),
(102,'Ireland','+353','IE',NULL,1),
(103,'Isle of Man','+44','IM',NULL,1),
(104,'Israel','+972','IL',NULL,1),
(105,'Italy','+39','IT',NULL,1),
(106,'Jamaica','+1876','JM',NULL,1),
(107,'Japan','+81','JP',NULL,1),
(108,'Jersey','+44','JE',NULL,1),
(109,'Jordan','+962','JO',NULL,1),
(110,'Kazakhstan','+77','KZ',NULL,1),
(111,'Kenya','+254','KE',NULL,1),
(112,'Kiribati','+686','KI',NULL,1),
(113,'Korea, Democratic People\'s Republic of Korea','+850','KP',NULL,1),
(114,'Korea, Republic of South Korea','+82','KR',NULL,1),
(115,'Kuwait','+965','KW',NULL,1),
(116,'Kyrgyzstan','+996','KG',NULL,1),
(117,'Laos','+856','LA',NULL,1),
(118,'Latvia','+371','LV',NULL,1),
(119,'Lebanon','+961','LB',NULL,1),
(120,'Lesotho','+266','LS',NULL,1),
(121,'Liberia','+231','LR',NULL,1),
(122,'Libyan Arab Jamahiriya','+218','LY',NULL,1),
(123,'Liechtenstein','+423','LI',NULL,1),
(124,'Lithuania','+370','LT',NULL,1),
(125,'Luxembourg','+352','LU',NULL,1),
(126,'Macao','+853','MO',NULL,1),
(127,'Macedonia','+389','MK',NULL,1),
(128,'Madagascar','+261','MG',NULL,1),
(129,'Malawi','+265','MW',NULL,1),
(130,'Malaysia','+60','MY',NULL,1),
(131,'Maldives','+960','MV',NULL,1),
(132,'Mali','+223','ML',NULL,1),
(133,'Malta','+356','MT',NULL,1),
(134,'Marshall Islands','+692','MH',NULL,1),
(135,'Martinique','+596','MQ',NULL,1),
(136,'Mauritania','+222','MR',NULL,1),
(137,'Mauritius','+230','MU',NULL,1),
(138,'Mayotte','+262','YT',NULL,1),
(139,'Mexico','+52','MX',NULL,1),
(140,'Micronesia, Federated States of Micronesia','+691','FM',NULL,1),
(141,'Moldova','+373','MD',NULL,1),
(142,'Monaco','+377','MC',NULL,1),
(143,'Mongolia','+976','MN',NULL,1),
(144,'Montenegro','+382','ME',NULL,1),
(145,'Montserrat','+1664','MS',NULL,1),
(146,'Morocco','+212','MA',NULL,1),
(147,'Mozambique','+258','MZ',NULL,1),
(148,'Myanmar','+95','MM',NULL,1),
(149,'Namibia','+264','NA',NULL,1),
(150,'Nauru','+674','NR',NULL,1),
(151,'Nepal','+977','NP',NULL,1),
(152,'Netherlands','+31','NL',NULL,1),
(153,'Netherlands Antilles','+599','AN',NULL,1),
(154,'New Caledonia','+687','NC',NULL,1),
(155,'New Zealand','+64','NZ',NULL,1),
(156,'Nicaragua','+505','NI',NULL,1),
(157,'Niger','+227','NE',NULL,1),
(158,'Nigeria','+234','NG',NULL,1),
(159,'Niue','+683','NU',NULL,1),
(160,'Norfolk Island','+672','NF',NULL,1),
(161,'Northern Mariana Islands','+1670','MP',NULL,1),
(162,'Norway','+47','NO',NULL,1),
(163,'Oman','+968','OM',NULL,1),
(164,'Pakistan','+92','PK',NULL,1),
(165,'Palau','+680','PW',NULL,1),
(166,'Palestinian Territory, Occupied','+970','PS',NULL,1),
(167,'Panama','+507','PA',NULL,1),
(168,'Papua New Guinea','+675','PG',NULL,1),
(169,'Paraguay','+595','PY',NULL,1),
(170,'Peru','+51','PE',NULL,1),
(171,'Philippines','+63','PH',NULL,1),
(172,'Pitcairn','+872','PN',NULL,1),
(173,'Poland','+48','PL',NULL,1),
(174,'Portugal','+351','PT',NULL,1),
(175,'Puerto Rico','+1939','PR',NULL,1),
(176,'Qatar','+974','QA',NULL,1),
(177,'Romania','+40','RO',NULL,1),
(178,'Russia','+7','RU',NULL,1),
(179,'Rwanda','+250','RW',NULL,1),
(180,'Reunion','+262','RE',NULL,1),
(181,'Saint Barthelemy','+590','BL',NULL,1),
(182,'Saint Helena, Ascension and Tristan Da Cunha','+290','SH',NULL,1),
(183,'Saint Kitts and Nevis','+1869','KN',NULL,1),
(184,'Saint Lucia','+1758','LC',NULL,1),
(185,'Saint Martin','+590','MF',NULL,1),
(186,'Saint Pierre and Miquelon','+508','PM',NULL,1),
(187,'Saint Vincent and the Grenadines','+1784','VC',NULL,1),
(188,'Samoa','+685','WS',NULL,1),
(189,'San Marino','+378','SM',NULL,1),
(190,'Sao Tome and Principe','+239','ST',NULL,1),
(191,'Saudi Arabia','+966','SA',NULL,1),
(192,'Senegal','+221','SN',NULL,1),
(193,'Serbia','+381','RS',NULL,1),
(194,'Seychelles','+248','SC',NULL,1),
(195,'Sierra Leone','+232','SL',NULL,1),
(196,'Singapore','+65','SG',NULL,1),
(197,'Slovakia','+421','SK',NULL,1),
(198,'Slovenia','+386','SI',NULL,1),
(199,'Solomon Islands','+677','SB',NULL,1),
(200,'Somalia','+252','SO',NULL,1),
(201,'South Africa','+27','ZA',NULL,1),
(202,'South Sudan','+211','SS',NULL,1),
(203,'South Georgia and the South Sandwich Islands','+500','GS',NULL,1),
(204,'Spain','+34','ES',NULL,1),
(205,'Sri Lanka','+94','LK',NULL,1),
(206,'Sudan','+249','SD',NULL,1),
(207,'Suriname','+597','SR',NULL,1),
(208,'Svalbard and Jan Mayen','+47','SJ',NULL,1),
(209,'Swaziland','+268','SZ',NULL,1),
(210,'Sweden','+46','SE',NULL,1),
(211,'Switzerland','+41','CH',NULL,1),
(212,'Syrian Arab Republic','+963','SY',NULL,1),
(213,'Taiwan','+886','TW',NULL,1),
(214,'Tajikistan','+992','TJ',NULL,1),
(215,'Tanzania, United Republic of Tanzania','+255','TZ',NULL,1),
(216,'Thailand','+66','TH',NULL,1),
(217,'Timor-Leste','+670','TL',NULL,1),
(218,'Togo','+228','TG',NULL,1),
(219,'Tokelau','+690','TK',NULL,1),
(220,'Tonga','+676','TO',NULL,1),
(221,'Trinidad and Tobago','+1868','TT',NULL,1),
(222,'Tunisia','+216','TN',NULL,1),
(223,'Turkey','+90','TR',NULL,1),
(224,'Turkmenistan','+993','TM',NULL,1),
(225,'Turks and Caicos Islands','+1649','TC',NULL,1),
(226,'Tuvalu','+688','TV',NULL,1),
(227,'Uganda','+256','UG',NULL,1),
(228,'Ukraine','+380','UA',NULL,1),
(229,'United Arab Emirates','+971','AE',NULL,1),
(230,'United Kingdom','+44','GB',NULL,1),
(231,'United States','+1','US',NULL,1),
(232,'Uruguay','+598','UY',NULL,1),
(233,'Uzbekistan','+998','UZ',NULL,1),
(234,'Vanuatu','+678','VU',NULL,1),
(235,'Venezuela, Bolivarian Republic of Venezuela','+58','VE',NULL,1),
(236,'Vietnam','+84','VN',NULL,1),
(237,'Virgin Islands, British','+1284','VG',NULL,1),
(238,'Virgin Islands, U.S.','+1340','VI',NULL,1),
(239,'Wallis and Futuna','+681','WF',NULL,1),
(240,'Yemen','+967','YE',NULL,1),
(241,'Zambia','+260','ZM',NULL,1),
(242,'Zimbabwe','+263','ZW',NULL,1);
/*!40000 ALTER TABLE `countries` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_boy_notifications`
--

DROP TABLE IF EXISTS `delivery_boy_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_boy_notifications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `delivery_boy_id` int(11) NOT NULL,
  `title` text NOT NULL,
  `message` text NOT NULL,
  `type` varchar(191) NOT NULL,
  `order_item_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_boy_notifications`
--

LOCK TABLES `delivery_boy_notifications` WRITE;
/*!40000 ALTER TABLE `delivery_boy_notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `delivery_boy_notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_boy_transactions`
--

DROP TABLE IF EXISTS `delivery_boy_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_boy_transactions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `order_id` int(11) DEFAULT NULL,
  `delivery_boy_id` int(11) DEFAULT NULL,
  `type` text DEFAULT NULL,
  `amount` double NOT NULL DEFAULT 0,
  `status` text DEFAULT NULL,
  `message` text DEFAULT NULL,
  `transaction_date` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_boy_transactions`
--

LOCK TABLES `delivery_boy_transactions` WRITE;
/*!40000 ALTER TABLE `delivery_boy_transactions` DISABLE KEYS */;
/*!40000 ALTER TABLE `delivery_boy_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `delivery_boys`
--

DROP TABLE IF EXISTS `delivery_boys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `delivery_boys` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `admin_id` bigint(20) unsigned DEFAULT NULL,
  `city_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(191) NOT NULL,
  `mobile` varchar(191) NOT NULL,
  `order_note` text DEFAULT NULL,
  `address` text NOT NULL,
  `bonus_type` int(11) DEFAULT 0 COMMENT '0 -> fixed/Salaried, 1 -> Commission',
  `bonus_percentage` double DEFAULT 0,
  `bonus_min_amount` double DEFAULT 0,
  `bonus_max_amount` double DEFAULT 0,
  `balance` double DEFAULT 0,
  `driving_license` text DEFAULT NULL,
  `national_identity_card` text DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `bank_account_number` text DEFAULT NULL,
  `bank_name` text DEFAULT NULL,
  `account_name` text DEFAULT NULL,
  `ifsc_code` text DEFAULT NULL,
  `other_payment_information` text DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1,
  `is_available` tinyint(4) NOT NULL DEFAULT 1,
  `fcm_id` varchar(191) DEFAULT NULL,
  `pincode_id` int(11) DEFAULT NULL,
  `cash_received` double NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `remark` text DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `delivery_boys`
--

LOCK TABLES `delivery_boys` WRITE;
/*!40000 ALTER TABLE `delivery_boys` DISABLE KEYS */;
/*!40000 ALTER TABLE `delivery_boys` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `devices`
--

DROP TABLE IF EXISTS `devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `devices` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `fcm_id` varchar(191) NOT NULL,
  `seller_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `devices`
--

LOCK TABLES `devices` WRITE;
/*!40000 ALTER TABLE `devices` DISABLE KEYS */;
/*!40000 ALTER TABLE `devices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_templates`
--

DROP TABLE IF EXISTS `email_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `email_templates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_templates`
--

LOCK TABLES `email_templates` WRITE;
/*!40000 ALTER TABLE `email_templates` DISABLE KEYS */;
INSERT INTO `email_templates` VALUES
(1,'Unlock 20% Off – Just for You!','Hi [First Name],\nWe’re excited to offer you an exclusive 20% discount on our most popular items! Don’t miss out – this special offer is only valid until [Date].\n\nClick below to shop and save:\n[Link Here]\n\nHurry, your discount awaits!','Exclusive Discount Offer','2025-06-20 14:17:32','2025-06-20 14:17:32');
/*!40000 ALTER TABLE `email_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `emails`
--

DROP TABLE IF EXISTS `emails`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `emails` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `message` longtext NOT NULL,
  `type` varchar(191) NOT NULL,
  `type_id` varchar(191) NOT NULL,
  `image` varchar(191) DEFAULT NULL,
  `date_sent` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `emails`
--

LOCK TABLES `emails` WRITE;
/*!40000 ALTER TABLE `emails` DISABLE KEYS */;
/*!40000 ALTER TABLE `emails` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `failed_jobs`
--

DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(191) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `failed_jobs`
--

LOCK TABLES `failed_jobs` WRITE;
/*!40000 ALTER TABLE `failed_jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `failed_jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `faqs`
--

DROP TABLE IF EXISTS `faqs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `faqs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `question` text NOT NULL,
  `answer` text NOT NULL,
  `status` char(191) DEFAULT '1',
  `seller_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `faqs`
--

LOCK TABLES `faqs` WRITE;
/*!40000 ALTER TABLE `faqs` DISABLE KEYS */;
/*!40000 ALTER TABLE `faqs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `favorites`
--

DROP TABLE IF EXISTS `favorites`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `favorites` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `favorites`
--

LOCK TABLES `favorites` WRITE;
/*!40000 ALTER TABLE `favorites` DISABLE KEYS */;
INSERT INTO `favorites` VALUES
(1,5,3,'2025-07-07 01:52:10','2025-07-07 01:52:10'),
(4,3,1,'2025-07-21 19:10:21','2025-07-21 19:10:21');
/*!40000 ALTER TABLE `favorites` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fund_transfers`
--

DROP TABLE IF EXISTS `fund_transfers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `fund_transfers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `delivery_boy_id` int(11) NOT NULL,
  `type` varchar(191) NOT NULL COMMENT 'credit | debit',
  `opening_balance` double NOT NULL,
  `closing_balance` double NOT NULL,
  `amount` double NOT NULL,
  `status` varchar(191) NOT NULL,
  `message` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fund_transfers`
--

LOCK TABLES `fund_transfers` WRITE;
/*!40000 ALTER TABLE `fund_transfers` DISABLE KEYS */;
/*!40000 ALTER TABLE `fund_transfers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invoice`
--

DROP TABLE IF EXISTS `invoice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `invoice` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `invoice_date` date NOT NULL,
  `order_id` int(11) NOT NULL,
  `name` text NOT NULL,
  `address` text NOT NULL,
  `order_date` datetime NOT NULL,
  `phone_number` varchar(191) NOT NULL,
  `order_list` text NOT NULL,
  `email` varchar(191) NOT NULL,
  `discount` varchar(191) NOT NULL,
  `total_sale` varchar(191) NOT NULL,
  `shipping_charge` varchar(191) NOT NULL,
  `payment` text NOT NULL,
  `order_item_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invoice`
--

LOCK TABLES `invoice` WRITE;
/*!40000 ALTER TABLE `invoice` DISABLE KEYS */;
/*!40000 ALTER TABLE `invoice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `jobs`
--

DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(191) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) unsigned NOT NULL,
  `reserved_at` int(10) unsigned DEFAULT NULL,
  `available_at` int(10) unsigned NOT NULL,
  `created_at` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `jobs`
--

LOCK TABLES `jobs` WRITE;
/*!40000 ALTER TABLE `jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `languages`
--

DROP TABLE IF EXISTS `languages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `languages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `supported_language_id` int(11) DEFAULT 0,
  `system_type` int(11) NOT NULL COMMENT '1 => Customer App, 2 => Seller and delivery boy App, 3 => Website, 4 => Admin panel',
  `json_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`json_data`)),
  `display_name` varchar(191) DEFAULT NULL,
  `is_default` int(11) DEFAULT 0 COMMENT '0 => No, 1 => Yes',
  `status` int(11) DEFAULT 1 COMMENT '0 => Deactive, 1 => Active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `languages`
--

LOCK TABLES `languages` WRITE;
/*!40000 ALTER TABLE `languages` DISABLE KEYS */;
/*!40000 ALTER TABLE `languages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `live_tracking`
--

DROP TABLE IF EXISTS `live_tracking`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `live_tracking` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) unsigned NOT NULL,
  `latitude` decimal(10,7) NOT NULL,
  `longitude` decimal(10,7) NOT NULL,
  `tracked_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `live_tracking_order_id_foreign` (`order_id`),
  CONSTRAINT `live_tracking_order_id_foreign` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `live_tracking`
--

LOCK TABLES `live_tracking` WRITE;
/*!40000 ALTER TABLE `live_tracking` DISABLE KEYS */;
/*!40000 ALTER TABLE `live_tracking` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mail_settings`
--

DROP TABLE IF EXISTS `mail_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mail_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_type` int(11) NOT NULL COMMENT '0:user, 1:Admin',
  `user_id` int(11) NOT NULL,
  `order_status_id` int(11) NOT NULL,
  `mail_status` int(11) NOT NULL COMMENT '0:false, 1:true',
  `mobile_status` int(11) NOT NULL COMMENT '0:false, 1:true',
  `sms_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0: Disabled, 1: Enabled',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mail_settings`
--

LOCK TABLES `mail_settings` WRITE;
/*!40000 ALTER TABLE `mail_settings` DISABLE KEYS */;
INSERT INTO `mail_settings` VALUES
(1,0,1,1,0,0,0,'2025-06-26 13:58:06','2025-06-26 13:58:06'),
(2,0,1,2,1,1,0,'2025-06-26 13:58:06','2025-06-26 13:58:06'),
(3,0,1,3,1,1,0,'2025-06-26 13:58:06','2025-06-26 13:58:06'),
(4,0,1,4,1,1,0,'2025-06-26 13:58:06','2025-06-26 13:58:06'),
(5,0,1,5,1,1,0,'2025-06-26 13:58:06','2025-06-26 13:58:06'),
(6,0,1,6,1,1,0,'2025-06-26 13:58:06','2025-06-26 13:58:06'),
(7,0,1,7,1,1,0,'2025-06-26 13:58:06','2025-06-26 13:58:06'),
(8,0,1,8,1,1,0,'2025-06-26 13:58:06','2025-06-26 13:58:06'),
(9,0,2,1,0,0,0,'2025-06-26 19:43:23','2025-06-26 19:43:23'),
(10,0,2,2,1,1,0,'2025-06-26 19:43:23','2025-06-26 19:43:23'),
(11,0,2,3,1,1,0,'2025-06-26 19:43:23','2025-06-26 19:43:23'),
(12,0,2,4,1,1,0,'2025-06-26 19:43:23','2025-06-26 19:43:23'),
(13,0,2,5,1,1,0,'2025-06-26 19:43:23','2025-06-26 19:43:23'),
(14,0,2,6,1,1,0,'2025-06-26 19:43:23','2025-06-26 19:43:23'),
(15,0,2,7,1,1,0,'2025-06-26 19:43:23','2025-06-26 19:43:23'),
(16,0,2,8,1,1,0,'2025-06-26 19:43:23','2025-06-26 19:43:23'),
(17,0,3,1,0,0,0,'2025-06-29 17:08:47','2025-06-29 17:08:47'),
(18,0,3,2,1,1,0,'2025-06-29 17:08:47','2025-06-29 17:08:47'),
(19,0,3,3,1,1,0,'2025-06-29 17:08:47','2025-06-29 17:08:47'),
(20,0,3,4,1,1,0,'2025-06-29 17:08:47','2025-06-29 17:08:47'),
(21,0,3,5,1,1,0,'2025-06-29 17:08:47','2025-06-29 17:08:47'),
(22,0,3,6,1,1,0,'2025-06-29 17:08:47','2025-06-29 17:08:47'),
(23,0,3,7,1,1,0,'2025-06-29 17:08:47','2025-06-29 17:08:47'),
(24,0,3,8,1,1,0,'2025-06-29 17:08:47','2025-06-29 17:08:47'),
(25,0,4,1,0,0,0,'2025-07-04 12:56:21','2025-07-04 12:56:21'),
(26,0,4,2,1,1,0,'2025-07-04 12:56:21','2025-07-04 12:56:21'),
(27,0,4,3,1,1,0,'2025-07-04 12:56:21','2025-07-04 12:56:21'),
(28,0,4,4,1,1,0,'2025-07-04 12:56:21','2025-07-04 12:56:21'),
(29,0,4,5,1,1,0,'2025-07-04 12:56:21','2025-07-04 12:56:21'),
(30,0,4,6,1,1,0,'2025-07-04 12:56:21','2025-07-04 12:56:21'),
(31,0,4,7,1,1,0,'2025-07-04 12:56:21','2025-07-04 12:56:21'),
(32,0,4,8,1,1,0,'2025-07-04 12:56:21','2025-07-04 12:56:21'),
(33,0,5,1,0,0,0,'2025-07-07 01:49:50','2025-07-07 01:49:50'),
(34,0,5,2,1,1,0,'2025-07-07 01:49:50','2025-07-07 01:49:50'),
(35,0,5,3,1,1,0,'2025-07-07 01:49:50','2025-07-07 01:49:50'),
(36,0,5,4,1,1,0,'2025-07-07 01:49:50','2025-07-07 01:49:50'),
(37,0,5,5,1,1,0,'2025-07-07 01:49:50','2025-07-07 01:49:50'),
(38,0,5,6,1,1,0,'2025-07-07 01:49:50','2025-07-07 01:49:50'),
(39,0,5,7,1,1,0,'2025-07-07 01:49:50','2025-07-07 01:49:50'),
(40,0,5,8,1,10,0,'2025-07-07 01:49:50','2025-07-07 01:59:47'),
(41,0,6,1,0,0,0,'2025-07-09 09:59:52','2025-07-09 09:59:52'),
(42,0,6,2,1,1,0,'2025-07-09 09:59:52','2025-07-09 09:59:52'),
(43,0,6,3,1,1,0,'2025-07-09 09:59:52','2025-07-09 09:59:52'),
(44,0,6,4,1,1,0,'2025-07-09 09:59:52','2025-07-09 09:59:52'),
(45,0,6,5,1,1,0,'2025-07-09 09:59:52','2025-07-09 09:59:52'),
(46,0,6,6,1,1,0,'2025-07-09 09:59:52','2025-07-09 09:59:52'),
(47,0,6,7,1,1,0,'2025-07-09 09:59:52','2025-07-09 09:59:52'),
(48,0,6,8,1,1,0,'2025-07-09 09:59:52','2025-07-09 09:59:52'),
(49,0,7,1,0,0,0,'2025-07-10 21:23:46','2025-07-10 21:23:46'),
(50,0,7,2,1,1,0,'2025-07-10 21:23:46','2025-07-10 21:23:46'),
(51,0,7,3,1,1,0,'2025-07-10 21:23:46','2025-07-10 21:23:46'),
(52,0,7,4,1,1,0,'2025-07-10 21:23:46','2025-07-10 21:23:46'),
(53,0,7,5,1,1,0,'2025-07-10 21:23:46','2025-07-10 21:23:46'),
(54,0,7,6,1,1,0,'2025-07-10 21:23:46','2025-07-10 21:23:46'),
(55,0,7,7,1,1,0,'2025-07-10 21:23:46','2025-07-10 21:23:46'),
(56,0,7,8,1,1,0,'2025-07-10 21:23:46','2025-07-10 21:23:46'),
(57,0,10,1,0,0,0,'2025-07-22 19:43:08','2025-07-22 19:43:08'),
(58,0,10,2,1,1,0,'2025-07-22 19:43:08','2025-07-22 19:43:08'),
(59,0,10,3,1,1,0,'2025-07-22 19:43:08','2025-07-22 19:43:08'),
(60,0,10,4,1,1,0,'2025-07-22 19:43:08','2025-07-22 19:43:08'),
(61,0,10,5,1,1,0,'2025-07-22 19:43:08','2025-07-22 19:43:08'),
(62,0,10,6,1,1,0,'2025-07-22 19:43:08','2025-07-22 19:43:08'),
(63,0,10,7,1,1,0,'2025-07-22 19:43:08','2025-07-22 19:43:08'),
(64,0,10,8,1,1,0,'2025-07-22 19:43:08','2025-07-22 19:43:08'),
(65,0,11,1,0,0,0,'2025-07-22 20:02:49','2025-07-22 20:02:49'),
(66,0,11,2,1,1,0,'2025-07-22 20:02:49','2025-07-22 20:02:49'),
(67,0,11,3,1,1,0,'2025-07-22 20:02:49','2025-07-22 20:02:49'),
(68,0,11,4,1,1,0,'2025-07-22 20:02:49','2025-07-22 20:02:49'),
(69,0,11,5,1,1,0,'2025-07-22 20:02:49','2025-07-22 20:02:49'),
(70,0,11,6,1,1,0,'2025-07-22 20:02:49','2025-07-22 20:02:49'),
(71,0,11,7,1,1,0,'2025-07-22 20:02:49','2025-07-22 20:02:49'),
(72,0,11,8,1,1,0,'2025-07-22 20:02:49','2025-07-22 20:02:49'),
(73,0,13,1,0,0,0,'2025-07-22 20:19:51','2025-07-22 20:19:51'),
(74,0,13,2,1,1,0,'2025-07-22 20:19:51','2025-07-22 20:19:51'),
(75,0,13,3,1,1,0,'2025-07-22 20:19:51','2025-07-22 20:19:51'),
(76,0,13,4,1,1,0,'2025-07-22 20:19:51','2025-07-22 20:19:51'),
(77,0,13,5,1,1,0,'2025-07-22 20:19:51','2025-07-22 20:19:51'),
(78,0,13,6,1,1,0,'2025-07-22 20:19:51','2025-07-22 20:19:51'),
(79,0,13,7,1,1,0,'2025-07-22 20:19:51','2025-07-22 20:19:51'),
(80,0,13,8,1,1,0,'2025-07-22 20:19:51','2025-07-22 20:19:51'),
(81,0,14,1,0,0,0,'2025-07-23 18:33:18','2025-07-23 18:33:18'),
(82,0,14,2,1,1,0,'2025-07-23 18:33:18','2025-07-23 18:33:18'),
(83,0,14,3,1,1,0,'2025-07-23 18:33:18','2025-07-23 18:33:18'),
(84,0,14,4,1,1,0,'2025-07-23 18:33:18','2025-07-23 18:33:18'),
(85,0,14,5,1,1,0,'2025-07-23 18:33:18','2025-07-23 18:33:18'),
(86,0,14,6,1,1,0,'2025-07-23 18:33:18','2025-07-23 18:33:18'),
(87,0,14,7,1,1,0,'2025-07-23 18:33:18','2025-07-23 18:33:18'),
(88,0,14,8,1,1,0,'2025-07-23 18:33:18','2025-07-23 18:33:18'),
(89,0,16,1,0,0,0,'2025-07-26 16:25:05','2025-07-26 16:25:05'),
(90,0,16,2,1,1,0,'2025-07-26 16:25:05','2025-07-26 16:25:05'),
(91,0,16,3,1,1,0,'2025-07-26 16:25:05','2025-07-26 16:25:05'),
(92,0,16,4,1,1,0,'2025-07-26 16:25:05','2025-07-26 16:25:05'),
(93,0,16,5,1,1,0,'2025-07-26 16:25:05','2025-07-26 16:25:05'),
(94,0,16,6,1,1,0,'2025-07-26 16:25:05','2025-07-26 16:25:05'),
(95,0,16,7,1,1,0,'2025-07-26 16:25:05','2025-07-26 16:25:05'),
(96,0,16,8,1,1,0,'2025-07-26 16:25:05','2025-07-26 16:25:05'),
(97,0,17,1,0,0,0,'2025-07-29 16:31:06','2025-07-29 16:31:06'),
(98,0,17,2,1,1,0,'2025-07-29 16:31:06','2025-07-29 16:31:06'),
(99,0,17,3,1,1,0,'2025-07-29 16:31:06','2025-07-29 16:31:06'),
(100,0,17,4,1,1,0,'2025-07-29 16:31:06','2025-07-29 16:31:06'),
(101,0,17,5,1,1,0,'2025-07-29 16:31:06','2025-07-29 16:31:06'),
(102,0,17,6,1,1,0,'2025-07-29 16:31:06','2025-07-29 16:31:06'),
(103,0,17,7,1,1,0,'2025-07-29 16:31:06','2025-07-29 16:31:06'),
(104,0,17,8,1,1,0,'2025-07-29 16:31:06','2025-07-29 16:31:06'),
(105,0,18,1,0,0,0,'2025-07-29 19:34:03','2025-07-29 19:34:03'),
(106,0,18,2,1,1,0,'2025-07-29 19:34:03','2025-07-29 19:34:03'),
(107,0,18,3,1,1,0,'2025-07-29 19:34:03','2025-07-29 19:34:03'),
(108,0,18,4,1,1,0,'2025-07-29 19:34:03','2025-07-29 19:34:03'),
(109,0,18,5,1,1,0,'2025-07-29 19:34:03','2025-07-29 19:34:03'),
(110,0,18,6,1,1,0,'2025-07-29 19:34:03','2025-07-29 19:34:03'),
(111,0,18,7,1,1,0,'2025-07-29 19:34:03','2025-07-29 19:34:03'),
(112,0,18,8,1,1,0,'2025-07-29 19:34:03','2025-07-29 19:34:03');
/*!40000 ALTER TABLE `mail_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `media`
--

DROP TABLE IF EXISTS `media`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `media` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` text NOT NULL,
  `extension` varchar(191) NOT NULL,
  `type` varchar(191) NOT NULL,
  `sub_directory` text NOT NULL,
  `size` text NOT NULL,
  `seller_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `media`
--

LOCK TABLES `media` WRITE;
/*!40000 ALTER TABLE `media` DISABLE KEYS */;
/*!40000 ALTER TABLE `media` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(191) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=128 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES
(1,'2014_10_12_000000_create_users_table',1),
(2,'2014_10_12_100000_create_password_resets_table',1),
(3,'2019_08_19_000000_create_failed_jobs_table',1),
(4,'2019_12_14_000001_create_personal_access_tokens_table',1),
(5,'2022_04_19_162005_create_admins_table',1),
(6,'2022_04_19_162451_create_areas_table',1),
(7,'2022_04_19_162712_create_carts_table',1),
(8,'2022_04_19_162902_create_categories_table',1),
(9,'2022_04_19_163557_create_cities_table',1),
(10,'2022_04_19_163656_create_delivery_boys_table',1),
(11,'2022_04_19_164613_create_delivery_boy_notifications_table',1),
(12,'2022_04_19_164915_create_devices_table',1),
(13,'2022_04_19_165016_create_faqs_table',1),
(14,'2022_04_19_165227_create_favorites_table',1),
(15,'2022_04_19_165315_create_fund_transfers_table',1),
(16,'2022_04_19_165516_create_invoices_table',1),
(17,'2022_04_19_165749_create_media_table',1),
(18,'2022_04_19_165926_create_newsletters_table',1),
(19,'2022_04_19_170143_create_notifications_table',1),
(20,'2022_04_19_170450_create_offers_table',1),
(21,'2022_04_19_170910_create_orders_table',1),
(22,'2022_04_19_172151_create_order_bank_transfers_table',1),
(23,'2022_04_19_172308_create_order_items_table',1),
(24,'2022_04_19_172843_create_order_trackings_table',1),
(25,'2022_04_20_151502_create_payments_table',1),
(26,'2022_04_20_151611_create_payment_requests_table',1),
(27,'2022_04_20_151716_create_pickup_locations_table',1),
(28,'2022_04_20_151845_create_pincodes_table',1),
(29,'2022_04_20_151954_create_products_table',1),
(30,'2022_04_20_152511_create_product_variants_table',1),
(31,'2022_04_20_152844_create_promo_codes_table',1),
(32,'2022_04_20_153012_create_return_requests_table',1),
(33,'2022_04_20_153129_create_sections_table',1),
(34,'2022_04_20_153246_create_sellers_table',1),
(35,'2022_04_20_154006_create_seller_commissions_table',1),
(36,'2022_04_20_154147_create_seller_transactions_table',1),
(37,'2022_04_20_154148_create_delivery_boy_transactions_table',1),
(38,'2022_04_20_154343_create_seller_wallet_transactions_table',1),
(39,'2022_04_20_154514_create_settings_table',1),
(40,'2022_04_20_154550_create_sliders_table',1),
(41,'2022_04_20_154642_create_social_media_table',1),
(42,'2022_04_20_154726_create_sub_categories_table',1),
(43,'2022_04_20_154923_create_taxes_table',1),
(44,'2022_04_20_155025_create_time_slots_table',1),
(45,'2022_04_20_155123_create_transactions_table',1),
(46,'2022_04_20_155303_create_units_table',1),
(47,'2022_04_20_155359_create_updates_table',1),
(48,'2022_04_20_155800_create_user_addresses_table',1),
(49,'2022_04_20_160100_create_wallet_transactions_table',1),
(50,'2022_04_20_160235_create_withdrawal_requests_table',1),
(51,'2022_05_23_061317_create_permission_categories_table',1),
(52,'2022_05_23_165755_create_permission_tables',1),
(53,'2022_06_04_070341_create_product_images_table',1),
(54,'2022_06_04_103202_create_user_tokens_table',1),
(55,'2022_07_05_174502_create_order_status_lists_table',1),
(56,'2022_07_09_074747_create_panel_notifications_table',1),
(57,'2022_08_16_180725_create_brands_table',1),
(58,'2022_08_24_160823_create_countries_table',1),
(59,'2022_10_01_055428_create_app_usages_table',1),
(60,'2022_11_15_062504_create_sessions_table',1),
(61,'2022_12_03_071819_add_remark_to_sellers_table',1),
(62,'2022_12_03_071820_add_fssai_lic_no_to_sellers_table',1),
(63,'2022_12_03_094442_add_remark_to_delivery_boys_table',1),
(64,'2022_12_03_104000_create_mail_settings_table',1),
(65,'2022_12_17_095005_create_admin_tokens_table',1),
(66,'2022_12_27_113410_create_jobs_table',1),
(67,'2022_19_01_060237_create_order_statuses_table',1),
(68,'2023_01_23_122915_add_row_order_to_sections_table',1),
(69,'2023_02_03_062618_add_type_link_to_notifications_table',1),
(70,'2023_04_04_101932_add_bonus_fields_delivery_boys_table',1),
(71,'2023_04_10_095427_add_delivery_boy_bonus_details_to_orders_table',1),
(72,'2023_04_17_114556_add_remark_to_withdrawal_requests_table',1),
(73,'2023_06_05_103829_create_supported_languages_table',1),
(74,'2023_06_05_110120_create_languages_table',1),
(75,'2023_07_12_091437_add_login_info_to_admins_table',1),
(76,'2023_08_21_091438_add_fssai_lic_no_to_products_table',1),
(77,'2023_08_25_091446_add_promo_code_id_to_orders_table',1),
(78,'2023_08_25_091447_add_display_name_to_languages_table',1),
(79,'2023_08_25_091448_make_alternate_mobile_nullable_in_user_addresses_table',1),
(80,'2023_08_25_091449_add_reason_to_return_requests_table',1),
(81,'2023_11_21_091448_create_product_ratings_table',1),
(82,'2023_11_21_091449_create_rating_images_table',1),
(83,'2023_11_22_091438_change_fssai_lic_no_to_sellers_table',1),
(84,'2023_11_22_091439_change_balance_to_sellers_table',1),
(85,'2024_02_08_071157_add_database_backup_to_permission_categories_table',1),
(86,'2024_02_08_073229_add_database_backup_download_to_permissions_table',1),
(87,'2024_02_13_073230_add_logo_to_countries_table',1),
(88,'2024_02_13_073231_add_type_to_offers_table',1),
(89,'2024_02_21_073231_add_transaction_to_wallet_transactions_table',1),
(90,'2024_05_03_073231_chnage_transaction_to_wallet_transactions_table',1),
(91,'2024_05_15_193349_update_category_slugs',1),
(92,'2024_05_30_191339_add_receipt_image_to_withdrawal_requests_table',1),
(93,'2024_06_05_185723_change_message_field_to_text_in_withdrawal_requests_table',1),
(94,'2024_06_25_100707_add_platform_to_user_tokens_table',1),
(95,'2024_06_25_130635_add_platform_to_admin_tokens_table',1),
(96,'2024_07_13_115457_add_type_to_users_table',1),
(97,'2024_07_17_164832_add_settings_login_data',1),
(98,'2024_07_23_171435_create_tags_table',1),
(99,'2024_07_23_182103_create_product_tag_table',1),
(100,'2024_07_23_182126_populate_tags_and_product_tag_table',1),
(101,'2024_07_29_111452_add_zone_to_cities_table',1),
(102,'2024_07_29_174919_change_city_id_type_in_sellers_table',1),
(103,'2024_08_11_142038_create_cart_notifications_table',1),
(104,'2024_08_27_145716_create_sms_verifications_table',1),
(105,'2024_08_31_115932_add_sms_status_to_mail_settings_table',1),
(106,'2024_08_31_145736_create_sms_templates_table',1),
(107,'2024_09_09_124731_remove_auth_id_from_users_table',1),
(108,'2024_09_27_123118_add_fields_to_sections_table',1),
(109,'2024_10_01_192703_create_live_tracking_table',1),
(110,'2024_10_02_163103_alter_fixed_charge_and_per_km_charge_in_cities_table',1),
(111,'2024_10_23_172210_add_email_verification_to_users_table',1),
(112,'2024_10_28_160050_add_barcode_to_products_table',1),
(113,'2025_01_09_145736_create_email_templates_table',1),
(114,'2025_01_09_170143_create_emails_table',1),
(115,'2025_02_05_225134_add_cancellation_reason_to_order_items_table',1),
(116,'2016_06_01_000001_create_oauth_auth_codes_table',2),
(117,'2016_06_01_000002_create_oauth_access_tokens_table',2),
(118,'2016_06_01_000003_create_oauth_refresh_tokens_table',2),
(119,'2016_06_01_000004_create_oauth_clients_table',2),
(120,'2016_06_01_000005_create_oauth_personal_access_clients_table',2),
(121,'2019_05_03_000001_create_customer_columns',3),
(122,'2019_05_03_000002_create_subscriptions_table',3),
(123,'2019_05_03_000003_create_subscription_items_table',3),
(124,'2024_12_22_000000_change_qty_to_decimal_in_carts_table',3),
(125,'2025_01_22_000000_add_subscription_fields_to_users_table',3),
(126,'2025_07_25_011447_change_quantity_to_decimal_in_order_items_table',4),
(127,'2025_07_25_011519_add_cut_selection_to_order_items_table',4);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `model_has_permissions`
--

DROP TABLE IF EXISTS `model_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) unsigned NOT NULL,
  `model_type` varchar(191) NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `model_has_permissions`
--

LOCK TABLES `model_has_permissions` WRITE;
/*!40000 ALTER TABLE `model_has_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `model_has_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `model_has_roles`
--

DROP TABLE IF EXISTS `model_has_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) unsigned NOT NULL,
  `model_type` varchar(191) NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`),
  CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `model_has_roles`
--

LOCK TABLES `model_has_roles` WRITE;
/*!40000 ALTER TABLE `model_has_roles` DISABLE KEYS */;
INSERT INTO `model_has_roles` VALUES
(1,'App\\Models\\Admin',1);
/*!40000 ALTER TABLE `model_has_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `newsletters`
--

DROP TABLE IF EXISTS `newsletters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `newsletters` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `email` text NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `newsletters`
--

LOCK TABLES `newsletters` WRITE;
/*!40000 ALTER TABLE `newsletters` DISABLE KEYS */;
/*!40000 ALTER TABLE `newsletters` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `message` varchar(191) NOT NULL,
  `type` varchar(191) NOT NULL,
  `type_id` int(11) NOT NULL,
  `type_link` varchar(191) DEFAULT NULL,
  `image` varchar(191) DEFAULT NULL,
  `date_sent` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
INSERT INTO `notifications` VALUES
(1,'What','Free','default',0,'','','2025-06-29 10:01:53');
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `oauth_access_tokens`
--

DROP TABLE IF EXISTS `oauth_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `oauth_access_tokens` (
  `id` varchar(100) NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `client_id` bigint(20) unsigned NOT NULL,
  `name` varchar(191) DEFAULT NULL,
  `scopes` text DEFAULT NULL,
  `revoked` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `oauth_access_tokens_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `oauth_access_tokens`
--

LOCK TABLES `oauth_access_tokens` WRITE;
/*!40000 ALTER TABLE `oauth_access_tokens` DISABLE KEYS */;
INSERT INTO `oauth_access_tokens` VALUES
('028e918e387cb41261eb488b264a168e11eada8d0bc72ac74bd0ce9f297b77a10298f3447872ba47',1,1,'authToken','[]',0,'2025-07-21 08:03:08','2025-07-21 08:03:08','2026-07-21 08:03:08'),
('05b158dc365bece41cad148a73d3792eb341954c249a4f45f6621a49ffdfd02c247d8d1dc9ff3b72',1,1,'authToken','[]',0,'2025-07-21 13:55:52','2025-07-21 13:55:52','2026-07-21 13:55:52'),
('0646de401f4e8fffc9d18e7b32819fa0b96dacea0b23d734b1e2ad2039ed39b9d70424b1bd1a41c1',3,1,'authToken','[]',0,'2025-07-12 19:42:20','2025-07-12 19:42:20','2026-07-12 19:42:20'),
('0c21babd41a2b99d7500bcf486900f155757efa35c9f598e6a71a98e2de5d069c41698e7a21c02f0',1,1,'authToken','[]',0,'2025-06-27 20:08:01','2025-06-27 20:08:01','2026-06-27 20:08:01'),
('0d01ccbd81b597887f2d07d2d6c41023feff5f7f148d135e6cfafc6f35d500d76275b5e174acf6b8',1,1,'authToken','[]',0,'2025-06-27 18:52:08','2025-06-27 18:52:08','2026-06-27 18:52:08'),
('0e46b32198c6bbcae327fba596383ff1306334e74a90574d99c1d215bafb3848238e8acf3d50fee6',1,1,'authToken','[]',0,'2025-07-27 17:24:24','2025-07-27 17:24:24','2026-07-27 17:24:24'),
('0f75992d7ae80ae80e84590a9ea03675b50fce57b4c60e22e1bb87d341ec305fd5a2586537eaf621',2,1,'authToken','[]',0,'2025-07-23 14:44:23','2025-07-23 14:44:23','2026-07-23 14:44:23'),
('103657dfdb11f11cc8b772c7783a512de380549bbf01d6c4301bd5e76756fea85f082034c6c1e073',1,1,'authToken','[]',0,'2025-07-01 19:35:49','2025-07-01 19:35:49','2026-07-01 19:35:49'),
('1084693a12fa6c7c53624a16b038c094d86147101bc28e4400014c7b12385d06ca73aa753fdcece1',1,1,'authToken','[]',0,'2025-07-12 11:40:03','2025-07-12 11:40:03','2026-07-12 11:40:03'),
('10a1beb4f3ac30378ba504b0d8be189a719be8310e27048ae80f24a93e19751c279415c027c7d0e8',1,1,'authToken','[]',0,'2025-07-28 15:17:19','2025-07-28 15:17:19','2026-07-28 15:17:19'),
('10be19426126d0269e5939678d185982e7d57c2868a65aabb381c94689612f1616603305d3ccf5a8',13,1,'authToken','[]',0,'2025-07-25 13:51:20','2025-07-25 13:51:20','2026-07-25 13:51:20'),
('110b3c2e3162697892a0c5abc398c9a63b6bf13ab786b1ebb56875b436733d0ce1ca1eb6e9d2637f',3,1,'authToken','[]',1,'2025-07-21 17:54:38','2025-07-21 19:54:09','2026-07-21 17:54:38'),
('120501b0de808d901262237cebf30d8955ca23c879373aa68b6b4494e72d7b3e9ec7ff1efd8537a4',1,1,'authToken','[]',0,'2025-06-25 20:18:38','2025-06-25 20:18:38','2026-06-25 20:18:38'),
('12110394b7fc7b0c5737d348a3d47a7372c91f07ecfa7a0f58f5f531e3414e12e932052e00565ec1',1,1,'authToken','[]',0,'2025-07-31 19:16:14','2025-07-31 19:16:14','2026-07-31 19:16:14'),
('134f9844c5a27c5da5391763956c8b8eabc6f43781e5c7e2cf1287499c2232fad0cd2bc527a09376',1,1,'authToken','[]',0,'2025-07-29 14:43:28','2025-07-29 14:43:28','2026-07-29 14:43:28'),
('13a61748207fa0d0ab1ecf7ad15399630a7b7e4ff234911dc5d4283619faf78254d4b7d9b54436c9',14,1,'authToken','[]',0,'2025-07-23 18:33:14','2025-07-23 18:33:14','2026-07-23 18:33:14'),
('1496fe49d5fda3601235d91eb15b039a7fc28b662d3af0f30771e9ff7244366f0287b7dc60b3e513',1,1,'authToken','[]',0,'2025-07-27 12:17:06','2025-07-27 12:17:06','2026-07-27 12:17:06'),
('157ea3c54573b77fd2aabfbd7a7b4c6c5a8e769f272de73ac2243abbbb73ea079927bba7828f54a1',1,1,'authToken','[]',0,'2025-07-26 20:24:15','2025-07-26 20:24:15','2026-07-26 20:24:15'),
('169dad286bdac2997edcd496279f3e1da173904e07294c280f020024d2b015e9fa1d54dfb2798c4a',2,1,'authToken','[]',0,'2025-06-27 18:29:05','2025-06-27 18:29:05','2026-06-27 18:29:05'),
('17d9926224430d840c16076dcffa7d38c915be41c53439803812a58cce322e50ef6cbfeff2cbc60f',1,1,'authToken','[]',0,'2025-06-27 19:43:27','2025-06-27 19:43:27','2026-06-27 19:43:27'),
('19bd119042d3af15f2b99587a23e3c41d9619f9b4fe37fab814c8036602f46a5daa571c14e5f3d05',1,1,'authToken','[]',0,'2025-07-29 16:31:55','2025-07-29 16:31:55','2026-07-29 16:31:55'),
('1ac29076e469c10c3e1ff67a2c9668751350250b3a6061507eb6276a56aaf4ae16e395b6b3413688',1,1,'authToken','[]',0,'2025-07-08 11:27:05','2025-07-08 11:27:05','2026-07-08 11:27:05'),
('1b1f7b8cdd065a2040531741e03c55822e7547445f1762c88c622527494406117d128f68d145f51f',1,1,'authToken','[]',0,'2025-06-28 16:36:13','2025-06-28 16:36:13','2026-06-28 16:36:13'),
('1c4421889ea81943cdbba87ff58fd050c31552a7334456d30ce7402e7219c5ec9bac7f6070749c5c',1,1,'authToken','[]',0,'2025-06-27 16:43:24','2025-06-27 16:43:24','2026-06-27 16:43:24'),
('1d800268a93b07abb3a7097de5b9830c63678be05c8ca2a34208428e7f8f3fcb1069ec0c0f69f2ef',1,1,'authToken','[]',0,'2025-07-29 13:26:43','2025-07-29 13:26:43','2026-07-29 13:26:43'),
('1dcdfe3e25d42c0fda878db344004d103849935ff1b83acacde6da058e284a8a6d2c578f20892259',2,1,'authToken','[]',0,'2025-07-06 12:42:52','2025-07-06 12:42:52','2026-07-06 12:42:52'),
('1fd76f12b904c3d68768c25eea3207badc4e342712aee26ea4fa3984efbaf09bde240373ef640926',1,1,'authToken','[]',0,'2025-07-08 06:47:24','2025-07-08 06:47:24','2026-07-08 06:47:24'),
('246a411c2fdf38ae1f163fb3b7adf01fc095e2142c57a541e71d771147fdd121aedeafa0b78a602d',1,1,'authToken','[]',0,'2025-07-10 16:47:00','2025-07-10 16:47:00','2026-07-10 16:47:00'),
('2647c2f77523e65bc2c7f251fb52a9272a392b322c0a8c7f7c296bbd7bd180431b3b1d8a9ebf1e81',3,1,'authToken','[]',0,'2025-06-27 20:26:24','2025-06-27 20:26:24','2026-06-27 20:26:24'),
('2780298147e87a5d6c1f3b8953079ad92820ed622d2a72f9d874044831c14ee12b228b71e10039c4',1,1,'authToken','[]',0,'2025-06-26 13:54:46','2025-06-26 13:54:46','2026-06-26 13:54:46'),
('29aad368cb65d87136188b17d2ae7443ea0c1a515dec95e03e93a87c81973058ba3f7a575bd3a331',1,1,'authToken','[]',0,'2025-07-03 22:30:24','2025-07-03 22:30:24','2026-07-03 22:30:24'),
('2a2c283062d610b7283d8c2822bb2400a4160266f719b420bc4f8c7d8fcf2c36163318c20a2d3bc0',1,1,'authToken','[]',0,'2025-06-26 19:55:06','2025-06-26 19:55:06','2026-06-26 19:55:06'),
('2b8f0b822045a5064661a2da6ea5401bb4291af3dfae1956416aea99fc36f2e5ce328e39b106e94a',5,1,'authToken','[]',0,'2025-07-07 01:49:45','2025-07-07 01:49:46','2026-07-07 01:49:45'),
('2d85261416742960ccf794b7f1baacdd6fb74034747e0e3027272316df951026364e4705a33fce71',2,1,'authToken','[]',0,'2025-06-27 18:47:55','2025-06-27 18:47:55','2026-06-27 18:47:55'),
('31f07dcce8ab28383acf7f15e82c09884bf8da0a879ae649bc05824deab7e1b433273e1ece307264',15,1,'authToken','[]',0,'2025-07-25 11:19:34','2025-07-25 11:19:34','2026-07-25 11:19:34'),
('34c21f7a53e69eef112a2c5de78c7e616d7de9e8c53aed264fda5641fa7c988909dffd87dd2090d2',2,1,'authToken','[]',0,'2025-07-13 12:31:43','2025-07-13 12:31:43','2026-07-13 12:31:43'),
('36230f9f029a7ebbe3080aacf875a0e41bfea93c39ebd77ac046beca287f4e096aa768e9ea831a06',1,1,'authToken','[]',0,'2025-07-06 19:04:38','2025-07-06 19:04:38','2026-07-06 19:04:38'),
('37300d0cf13094ec75490f8f9367b4a74211ae32fe854194a910d09c5e2f28073e2ac0ff2cd5613b',3,1,'authToken','[]',0,'2025-07-21 01:23:14','2025-07-21 01:23:14','2026-07-21 01:23:14'),
('3ae839bece85124408d97d98193797491cd8e397f8277b467be8182b713ef4efff0adbc07f471bf7',1,1,'authToken','[]',0,'2025-07-25 15:22:18','2025-07-25 15:22:18','2026-07-25 15:22:18'),
('3f4cfbcd61c5bc4b95babf75b547c0ecb2b900c4888cf07a0f85785bc569a79c016324f26b5e6b17',2,1,'authToken','[]',0,'2025-06-26 19:43:20','2025-06-26 19:43:20','2026-06-26 19:43:20'),
('40239e6f16030ae5220918c8eb0836296d2ffe71c8fb31c00cc34e618d1c84e9ac5612395b101721',1,1,'authToken','[]',0,'2025-07-05 15:17:32','2025-07-05 15:17:32','2026-07-05 15:17:32'),
('433218c0f1e76c1666d8022f786db968fe176fb1f3b685dc24748939782eaf9a1c6b4f5e6f29eea7',1,1,'authToken','[]',0,'2025-07-02 11:33:52','2025-07-02 11:33:52','2026-07-02 11:33:52'),
('436f75f98522a377c3431c70f1b82bfcbafd9330fd4cc1c3caa9695f9f3496fd3ff63bcec6be2ee3',2,1,'authToken','[]',0,'2025-06-28 16:40:10','2025-06-28 16:40:10','2026-06-28 16:40:10'),
('43bb912f4337bc93674f48717c9a8d0e43a700e4e4ebc313080beb4fccce544fc789931b2b155322',1,1,'authToken','[]',0,'2025-07-31 13:34:26','2025-07-31 13:34:26','2026-07-31 13:34:26'),
('442b2c307e22772967faf6f37bb96a5276070018344355e67251f991d545ef9c2caaad950cdb66e6',1,1,'authToken','[]',0,'2025-06-26 09:42:37','2025-06-26 09:42:37','2026-06-26 09:42:37'),
('4aaa4dd9443238fbc76a3b6427f019a60489e3bf1e411d89b2b39861f576b8e3d8196e75956ec8e8',17,1,'authToken','[]',0,'2025-07-29 16:31:02','2025-07-29 16:31:02','2026-07-29 16:31:02'),
('4f91e4e0df04664ea97a9688384d9a4ae93adc145c91e4ec6e8ada9ff441632daa642b9979773eea',1,1,'authToken','[]',0,'2025-07-06 12:51:49','2025-07-06 12:51:49','2026-07-06 12:51:49'),
('50582e5687ca1b3f6f50eeba7370f3ce1e0acc1eb77ecce2de50c610c3466d38303820b221bff405',1,1,'authToken','[]',0,'2025-07-13 12:59:52','2025-07-13 12:59:52','2026-07-13 12:59:52'),
('508f8ab63753d6fe110ba1108ada8f77eb7e4720e8ed6894acaabc4a4ddfa12142b2e548534e1c8d',1,1,'authToken','[]',0,'2025-07-30 13:17:01','2025-07-30 13:17:01','2026-07-30 13:17:01'),
('52f7bf20e978f9eef9ee3ebcca2a97ec7dce9fbc16d242e81fc6b5afc64953cccf2ca0f3a2013c4a',1,1,'authToken','[]',0,'2025-06-26 13:58:03','2025-06-26 13:58:03','2026-06-26 13:58:03'),
('55dfc9909c6241c482471661178e093d16317b4c476e368a8e125523b4c8db990c6770302f27baf3',1,1,'authToken','[]',0,'2025-06-30 09:23:34','2025-06-30 09:23:34','2026-06-30 09:23:34'),
('560114cf1534f03a65be9c98c81f9f8f79ea5118492615bb499e461f64ef66e03993633354dd6577',1,1,'authToken','[]',0,'2025-07-25 15:12:02','2025-07-25 15:12:02','2026-07-25 15:12:02'),
('57043c43efd34e3eb88b70eb27c353546a5cfceb8976c8b1a8f22b8beb3d3943bb579785760305a0',3,1,'authToken','[]',0,'2025-07-20 21:21:28','2025-07-20 21:21:28','2026-07-20 21:21:28'),
('59207198406f2e599e965506337ea0024e1c3a9f86de1d4ef0792637a99582d216f4ca94b7d9bc09',1,1,'authToken','[]',0,'2025-07-27 17:23:59','2025-07-27 17:23:59','2026-07-27 17:23:59'),
('5964ad8cc01e3d6a3995435c85e02f07d0087a7df61731cf5c1572967f7529ece9672bf3b43bd1ba',1,1,'authToken','[]',0,'2025-07-02 11:31:48','2025-07-02 11:31:48','2026-07-02 11:31:48'),
('5a1bda7351d1fc0fd37cd447cf63ba7d6a85c1bd70b54bf0b20b5e582cbbf0d2e48e7777f729ccdc',6,1,'authToken','[]',0,'2025-07-09 09:59:40','2025-07-09 09:59:40','2026-07-09 09:59:40'),
('5d05e076a3cdc3b0fda67579781ffc9ffc77b2074a634277ee678be4df407b62110cd0eed5fbe23a',1,1,'authToken','[]',0,'2025-07-31 08:48:19','2025-07-31 08:48:19','2026-07-31 08:48:19'),
('607fac871d197e222e14f311ed10f2c509fb9f778f84beb4243b8b03f4eeb31c7ae2fd3132fcf931',1,1,'authToken','[]',0,'2025-07-02 14:07:54','2025-07-02 14:07:54','2026-07-02 14:07:54'),
('6ac3fa1421db42a6ba1fcb41fc0c52ed62091ebd47bdccc5cb7d78d7fe65447a4b758cdea074eac2',1,1,'authToken','[]',0,'2025-07-28 20:20:56','2025-07-28 20:20:56','2026-07-28 20:20:56'),
('73acd74f05f5e270ba4cb6ac3b57f3e1b0fecc1ebc0b15828ce53fbbf04830c68d04e0cb26ecd87f',8,1,'authToken','[]',0,'2025-07-22 19:18:00','2025-07-22 19:18:00','2026-07-22 19:18:00'),
('7436b5b833ce792531d9ed3e622997a93d736d6a70087ef71f4bbee235c3eb3ad2dffa96da7c185a',1,1,'authToken','[]',0,'2025-07-22 13:03:35','2025-07-22 13:03:35','2026-07-22 13:03:35'),
('76e2ecb3bae79d67598ba555c5aaed6a061b086db2275d687508ef07540c6fecffa0216707d8b93b',1,1,'authToken','[]',0,'2025-07-13 20:50:39','2025-07-13 20:50:39','2026-07-13 20:50:39'),
('788c656e90435a241ab3f696010b89e9b741622ab20fc19f261a183d4eaf71b7a6d3fce1389cc5e4',3,1,'authToken','[]',0,'2025-07-13 09:20:44','2025-07-13 09:20:44','2026-07-13 09:20:44'),
('78a80ab73657786447f55032d35a32eaeb1422b0d8e6430eb0d8ecec8b9dbf95c2f8fc39bcc3e77e',9,1,'authToken','[]',0,'2025-07-22 19:18:14','2025-07-22 19:18:14','2026-07-22 19:18:14'),
('7dbe5b21660eef7c36d4c8a96a19baba8a2abe2d3e69b87667e18626ca67a054927a19dba5256d90',1,1,'authToken','[]',0,'2025-07-23 18:41:49','2025-07-23 18:41:49','2026-07-23 18:41:49'),
('7dd3a16182f87342bdfe13ae320af5f568ee058172829ae6200fddec848bc3097a9313d0a317ca3a',1,1,'authToken','[]',0,'2025-07-22 13:56:57','2025-07-22 13:56:57','2026-07-22 13:56:57'),
('7e160d30385d7a2f213b8d732619776297fe9b994d21bb7137d67c118166f8117d6621e54be69f10',1,1,'authToken','[]',0,'2025-07-29 13:27:48','2025-07-29 13:27:48','2026-07-29 13:27:48'),
('81cc1541eb3b6db6772994f3bbc811d5c6d88e55d2e5ccd09419fa157a1880434f5e27f08470776d',1,1,'authToken','[]',0,'2025-06-26 19:53:33','2025-06-26 19:53:33','2026-06-26 19:53:33'),
('81fbc1883ac81fd6645ef9f35019be765f505fe066d3efaa0411e4ee8247a756b6a56d905d823438',1,1,'authToken','[]',0,'2025-06-29 15:23:24','2025-06-29 15:23:24','2026-06-29 15:23:24'),
('84e721462f9a4eed6e9d359c87ac376c57fa766b782df4537f447b58870cd2577ad0b559c0769625',1,1,'authToken','[]',0,'2025-07-21 14:07:42','2025-07-21 14:07:42','2026-07-21 14:07:42'),
('876fc7dc9d628759ace2664b6d1f91f22521d22108e3934b248b45f95a3dd9ee47925c82c82e8c1a',1,1,'authToken','[]',0,'2025-07-02 14:07:31','2025-07-02 14:07:31','2026-07-02 14:07:31'),
('8cfbe525b425e0754b981af7caa26c2351f581389744dc422377f7d9cd2d116dcdeca45ff44fb993',4,1,'authToken','[]',0,'2025-07-04 12:56:15','2025-07-04 12:56:15','2026-07-04 12:56:15'),
('8fc5448329fe28d9c995cca9fdc093c762e67c45ce37331234373dafa3f0651659a5f6915ad455d6',7,1,'authToken','[]',1,'2025-07-10 21:23:40','2025-07-10 21:25:20','2026-07-10 21:23:40'),
('9134876821411c06978f0b0b553a9ea509caa4696365e50f7bdf977fca758be233283449fd9b5130',7,1,'authToken','[]',0,'2025-07-10 21:26:00','2025-07-10 21:26:00','2026-07-10 21:26:00'),
('9183d7129d10ea664cf73e340bb63b55cfa2c2afe11e0138d00176b11c3fcb021342ecc5db680644',1,1,'authToken','[]',0,'2025-06-28 20:19:27','2025-06-28 20:19:27','2026-06-28 20:19:27'),
('94109c065222bdd3b68367a9e649d1db9ab53f94e4c40f7bc67ef4fa99b505a10d62340836216894',11,1,'authToken','[]',0,'2025-07-22 20:01:46','2025-07-22 20:01:46','2026-07-22 20:01:46'),
('9484290cf97fdd2fe9eee464daadccdf15e340f92e9da60061bb8ccb3fec85dde698785ecfcdeefc',1,1,'authToken','[]',0,'2025-07-23 15:37:34','2025-07-23 15:37:34','2026-07-23 15:37:34'),
('952e478b49fb361c605cf6222206b3265db4c06653f41c7d0c22ce39fba0173bc4cede5f7055a9be',1,1,'authToken','[]',0,'2025-07-01 15:11:46','2025-07-01 15:11:46','2026-07-01 15:11:46'),
('9ceb21e00046fb513d86ee9b89cc2a94c5ccb30592a8b27849ed7515e96e3621d58a75c46988142d',1,1,'authToken','[]',0,'2025-07-28 14:45:17','2025-07-28 14:45:17','2026-07-28 14:45:17'),
('a118b686c0429f5229b92b7e90eddefb79e38d134ade6b76c45e262e1f3f2cc0f42155ec85d2ba04',1,1,'authToken','[]',0,'2025-06-27 18:30:44','2025-06-27 18:30:44','2026-06-27 18:30:44'),
('a191c3555883022bbcf48d0738bab68caa3c593892ace4d319a2eee4891d7a528503d0432bc05f94',3,1,'authToken','[]',0,'2025-07-12 01:59:51','2025-07-12 01:59:51','2026-07-12 01:59:51'),
('a49e11cf29cacbdbf5bf35dd9262346a869280b096188b53462c4a25338b54c91e463f6338394171',12,1,'authToken','[]',0,'2025-07-22 20:06:44','2025-07-22 20:06:44','2026-07-22 20:06:44'),
('a8647ab41e8cea73e85a6d934dc7f5087f4b2d6e09e3b979893cd685fdeaf1cf4caf706a73f36a12',1,1,'authToken','[]',0,'2025-07-29 18:53:46','2025-07-29 18:53:46','2026-07-29 18:53:46'),
('a928a23c4f7c7cb8ee1519b4323356f25a0829b02dc81d294c68419f714e38f3f7f05e9877990897',1,1,'authToken','[]',0,'2025-07-12 20:39:03','2025-07-12 20:39:03','2026-07-12 20:39:03'),
('aae95a55821fffc4f1077d3e86cb48e797681eed7216542f30622300356c0a09480af56e06d67b0a',1,1,'authToken','[]',0,'2025-06-26 12:29:50','2025-06-26 12:29:50','2026-06-26 12:29:50'),
('ab07f1823656edc29652f0db755822f115b5d5076ced5f8dbceaddd76f0ccf6d3c0a333dd2fb41bf',1,1,'authToken','[]',0,'2025-06-28 17:08:09','2025-06-28 17:08:09','2026-06-28 17:08:09'),
('ad47c6ca7d1c3cab92a411657e454bd61858fe57b893ad8fc992f708c7a072f28c50231e146f4730',3,1,'authToken','[]',1,'2025-07-13 01:49:33','2025-07-13 02:00:45','2026-07-13 01:49:33'),
('b8da7e017e44b2da0c1e7861d097db954bf4c30b3b7172c617006b37c809333ca0564d1692274abd',1,1,'authToken','[]',0,'2025-07-27 17:24:45','2025-07-27 17:24:45','2026-07-27 17:24:45'),
('ba09d9021124e99f5430894d4da52caacfc8186054ecbbe27667956573a1e3d08483ec490ee7fcad',10,1,'authToken','[]',0,'2025-07-22 19:37:56','2025-07-22 19:37:56','2026-07-22 19:37:56'),
('bd5859c4b73e88ebec1f32af718b195797adc3d31f8d228325bb4607f252a3a17748a4ba460f797c',1,1,'authToken','[]',0,'2025-07-31 16:26:07','2025-07-31 16:26:07','2026-07-31 16:26:07'),
('c2496d3beb844d4635994f4cef0f4e191465fa88089a32cfc694532e5143f583054d98e7202dc104',1,1,'authToken','[]',0,'2025-07-02 17:04:35','2025-07-02 17:04:35','2026-07-02 17:04:35'),
('c2fd2b77f2327d1f90f3896c0f16b0b57d6eb91309573a522f101ea0f2011cadf6bdbc2b7ef889ee',1,1,'authToken','[]',0,'2025-06-27 17:57:07','2025-06-27 17:57:07','2026-06-27 17:57:07'),
('c3b75211fdafdb45d8f4ffae3c234d1e4c6da143895164c8a09d9bdab3cbea9c750890eb0c5f8d2f',2,1,'authToken','[]',0,'2025-07-25 14:23:06','2025-07-25 14:23:06','2026-07-25 14:23:06'),
('c49f3cff86613734a0f0e4d7eca3b4cc67bc72f30b57ddcaa932e203abacb6c372bc9a0906bb3f1b',3,1,'authToken','[]',1,'2025-07-12 00:45:49','2025-07-12 00:46:25','2026-07-12 00:45:49'),
('c529677cc4c9f090a6c001a25788de8dc90f1707a7a4e11f4229c175b27866553dbf391b4c715a50',3,1,'authToken','[]',0,'2025-07-03 20:55:23','2025-07-03 20:55:23','2026-07-03 20:55:23'),
('c916d8e344c55679757810d1154d76df62f11f777158311f67996d0c7924b80c3cf2a27d13a17d03',18,1,'authToken','[]',0,'2025-07-29 19:31:32','2025-07-29 19:31:32','2026-07-29 19:31:32'),
('ca36743ee8776a4e50f1d435571b5dae96976f925d47226ab564b62ecb232bc4ff6418249746ebe1',1,1,'authToken','[]',0,'2025-07-10 16:47:00','2025-07-10 16:47:00','2026-07-10 16:47:00'),
('cbd8332ffdac47c640c1af69400965ab1b30eeae07ac4965ef628489dee7a06bab02237b2988f998',1,1,'authToken','[]',0,'2025-06-20 14:18:36','2025-06-20 14:18:36','2026-06-20 14:18:36'),
('cfcb6ff6adf8fffe7027b2bcd087b0fb56a16a556b42dac8e03bf2d585c340a184feb2b5e607cc6a',1,1,'authToken','[]',0,'2025-07-08 16:27:48','2025-07-08 16:27:48','2026-07-08 16:27:48'),
('d8d24469b2fe687cf0b854e7cc1a96084269b38e90c75ad9bcd9ea103ed181d402dd2b1405f73909',1,1,'authToken','[]',0,'2025-07-05 18:15:49','2025-07-05 18:15:49','2026-07-05 18:15:49'),
('da58a17ddf4da9932f4915bf3d3d25b8aee9dbce682d68ad1696909e673c9abdfb6dbe25c03da162',1,1,'authToken','[]',0,'2025-07-12 20:22:25','2025-07-12 20:22:25','2026-07-12 20:22:25'),
('db2ff007a241690f7c114c811871d76c44f361a395e9c72518ddfcb4169177fa9f33c74ba0a58ac6',1,1,'authToken','[]',0,'2025-06-24 07:28:47','2025-06-24 07:28:47','2026-06-24 07:28:47'),
('de996b4cc73167b9f6d4ea63be9054b3da427132ed728d5de841607559af033b19e6c4086d7a644f',1,1,'authToken','[]',0,'2025-07-13 07:02:36','2025-07-13 07:02:36','2026-07-13 07:02:36'),
('e0ee5dc65c18a92814a64df106a4005b5527670fdc7892628de7e04b6ed1232918e2121877a8999c',1,1,'authToken','[]',0,'2025-07-28 15:19:56','2025-07-28 15:19:56','2026-07-28 15:19:56'),
('e1fea97cedeb38f03773aef229f235db00f270d40efaecf3f19c23559215e4792558aed5324f6613',1,1,'authToken','[]',0,'2025-07-27 17:16:39','2025-07-27 17:16:39','2026-07-27 17:16:39'),
('e38e9d0a95f9718a720822d428ef65367e391c466bbc8a489685f2efb318af6625c077cf3c4f75c8',16,1,'authToken','[]',0,'2025-07-26 16:25:01','2025-07-26 16:25:01','2026-07-26 16:25:01'),
('ea508b80ae054b2b8afca2e83cb9b5d1a73254fcc3696a88e763a3c5ed360f89f07e6ec7e5f21c92',1,1,'authToken','[]',0,'2025-06-27 17:04:22','2025-06-27 17:04:22','2026-06-27 17:04:22'),
('eb05f5ed1e26f9742e12c9a3d007aac9b3c83f17d5f471531bc755a7a7bd220664b0183f1e9680ff',1,1,'authToken','[]',0,'2025-06-30 08:56:24','2025-06-30 08:56:24','2026-06-30 08:56:24'),
('eb6c1a75300ca456864c0cf86a990cc5c2f1d944172195724760ffa5f1399317ab027c9964314a5d',1,1,'authToken','[]',0,'2025-07-31 15:08:20','2025-07-31 15:08:20','2026-07-31 15:08:20'),
('ec6409ff5d5b7691b566135e570a7df67e97d6448176b66474ca9ad4d81773e80d4a482e51c05116',1,1,'authToken','[]',0,'2025-07-09 16:00:30','2025-07-09 16:00:30','2026-07-09 16:00:30'),
('ecdec724b87e97c100beec5b450579d9feea5f6d5424213be09502253ede972cd50e2ec0e9e0bda1',3,1,'authToken','[]',0,'2025-07-06 12:31:34','2025-07-06 12:31:34','2026-07-06 12:31:34'),
('ed707f3a07f3d1c6a66049f49b611f34f23c50b7488299c4bc7ef11b79509c00db2d940e3ce53a06',1,1,'authToken','[]',0,'2025-07-25 13:47:49','2025-07-25 13:47:49','2026-07-25 13:47:49'),
('edf9c2069ecfdfaf1e503b70173d2ad1ae13e711164bd02ed17dda8621caf1c077af3989ec1b5d9e',1,1,'authToken','[]',0,'2025-06-20 15:21:54','2025-06-20 15:21:54','2026-06-20 15:21:54'),
('f003e90dadff2dcfff60d5a2a046b01fb07820f1ced2ff59deb03e86626f5476ee541625e9d046e5',13,1,'authToken','[]',0,'2025-07-22 20:12:56','2025-07-22 20:12:56','2026-07-22 20:12:56'),
('f03f778c697d581559d65312e46ef8129fad1a9b22f1c97ee19701205714038cf640cd7a213454af',1,1,'authToken','[]',0,'2025-07-10 16:06:00','2025-07-10 16:06:01','2026-07-10 16:06:00'),
('f19e8cacaa6d089be5a766b5f1311a8c8f53a61b0c0c431995c74459c5725f986dfa580582b6b1a4',1,1,'authToken','[]',0,'2025-07-29 19:24:38','2025-07-29 19:24:38','2026-07-29 19:24:38'),
('f62baf6f0eeadab7045db705ff7e38164e3e9ae1ad325808b804ab7af82f7f7e304bf099803f8bab',1,1,'authToken','[]',0,'2025-07-09 15:38:09','2025-07-09 15:38:09','2026-07-09 15:38:09'),
('f7179a683f4c9416feb796dd313f15811fbddf91c9ba23a608b6cebe03607a9d8de8f82179c6c41a',1,1,'authToken','[]',0,'2025-06-26 14:25:34','2025-06-26 14:25:34','2026-06-26 14:25:34'),
('fa34563717db5d2a38dc61ff07967c2c5a9216f17d435d7cc8c39d61a2f96d8b3bec49fb21c300d4',1,1,'authToken','[]',0,'2025-07-31 16:22:08','2025-07-31 16:22:08','2026-07-31 16:22:08'),
('ff265c605d23a340470c01ffa7545450894cda785654fce4cc5875fad920b70e70ede849d5be8f98',1,1,'authToken','[]',0,'2025-07-23 18:41:50','2025-07-23 18:41:50','2026-07-23 18:41:50');
/*!40000 ALTER TABLE `oauth_access_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `oauth_auth_codes`
--

DROP TABLE IF EXISTS `oauth_auth_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `oauth_auth_codes` (
  `id` varchar(100) NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `client_id` bigint(20) unsigned NOT NULL,
  `scopes` text DEFAULT NULL,
  `revoked` tinyint(1) NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `oauth_auth_codes_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `oauth_auth_codes`
--

LOCK TABLES `oauth_auth_codes` WRITE;
/*!40000 ALTER TABLE `oauth_auth_codes` DISABLE KEYS */;
/*!40000 ALTER TABLE `oauth_auth_codes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `oauth_clients`
--

DROP TABLE IF EXISTS `oauth_clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `oauth_clients` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(191) NOT NULL,
  `secret` varchar(100) DEFAULT NULL,
  `provider` varchar(191) DEFAULT NULL,
  `redirect` text NOT NULL,
  `personal_access_client` tinyint(1) NOT NULL,
  `password_client` tinyint(1) NOT NULL,
  `revoked` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `oauth_clients_user_id_index` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `oauth_clients`
--

LOCK TABLES `oauth_clients` WRITE;
/*!40000 ALTER TABLE `oauth_clients` DISABLE KEYS */;
INSERT INTO `oauth_clients` VALUES
(1,NULL,'eGrocer Personal Access Client','OVebAsBdJRoOBqC7nioJSnk5MC0oK2tCXv8McSC0',NULL,'http://localhost',1,0,0,'2025-06-20 14:17:34','2025-06-20 14:17:34'),
(2,NULL,'eGrocer Password Grant Client','POMYYI1HnCYmWSlafSwTu9PiJl2yXFlFoBgPQhZo','users','http://localhost',0,1,0,'2025-06-20 14:17:34','2025-06-20 14:17:34');
/*!40000 ALTER TABLE `oauth_clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `oauth_personal_access_clients`
--

DROP TABLE IF EXISTS `oauth_personal_access_clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `oauth_personal_access_clients` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `client_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `oauth_personal_access_clients`
--

LOCK TABLES `oauth_personal_access_clients` WRITE;
/*!40000 ALTER TABLE `oauth_personal_access_clients` DISABLE KEYS */;
INSERT INTO `oauth_personal_access_clients` VALUES
(1,1,'2025-06-20 14:17:34','2025-06-20 14:17:34');
/*!40000 ALTER TABLE `oauth_personal_access_clients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `oauth_refresh_tokens`
--

DROP TABLE IF EXISTS `oauth_refresh_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `oauth_refresh_tokens` (
  `id` varchar(100) NOT NULL,
  `access_token_id` varchar(100) NOT NULL,
  `revoked` tinyint(1) NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `oauth_refresh_tokens_access_token_id_index` (`access_token_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `oauth_refresh_tokens`
--

LOCK TABLES `oauth_refresh_tokens` WRITE;
/*!40000 ALTER TABLE `oauth_refresh_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `oauth_refresh_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `offers`
--

DROP TABLE IF EXISTS `offers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `offers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `image` varchar(191) NOT NULL,
  `position` varchar(191) NOT NULL,
  `section_position` varchar(191) NOT NULL,
  `type` varchar(191) NOT NULL,
  `type_id` varchar(191) NOT NULL,
  `offer_url` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `offers`
--

LOCK TABLES `offers` WRITE;
/*!40000 ALTER TABLE `offers` DISABLE KEYS */;
INSERT INTO `offers` VALUES
(4,'offers/1751955367_91108.jpg','below_slider','0','category','1','','2025-07-08 11:46:07','2025-07-27 13:31:26');
/*!40000 ALTER TABLE `offers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_bank_transfers`
--

DROP TABLE IF EXISTS `order_bank_transfers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_bank_transfers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `attachment` longtext NOT NULL,
  `message` text DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_bank_transfers`
--

LOCK TABLES `order_bank_transfers` WRITE;
/*!40000 ALTER TABLE `order_bank_transfers` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_bank_transfers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_items`
--

DROP TABLE IF EXISTS `order_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `orders_id` varchar(191) NOT NULL,
  `product_name` text DEFAULT NULL,
  `variant_name` text DEFAULT NULL,
  `cut_selection` varchar(191) DEFAULT NULL,
  `product_variant_id` int(11) NOT NULL,
  `delivery_boy_id` int(11) DEFAULT 0,
  `quantity` decimal(8,2) NOT NULL,
  `price` double(8,2) NOT NULL,
  `discounted_price` double NOT NULL,
  `tax_amount` double(8,2) NOT NULL DEFAULT 0.00,
  `tax_percentage` double(8,2) NOT NULL DEFAULT 0.00,
  `discount` double(8,2) NOT NULL DEFAULT 0.00,
  `sub_total` double(8,2) NOT NULL,
  `status` varchar(191) NOT NULL,
  `active_status` varchar(191) NOT NULL,
  `cancellation_reason` text DEFAULT NULL,
  `canceled_at` timestamp NULL DEFAULT NULL,
  `seller_id` int(11) NOT NULL,
  `is_credited` tinyint(4) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `order_items_user_id_index` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_items`
--

LOCK TABLES `order_items` WRITE;
/*!40000 ALTER TABLE `order_items` DISABLE KEYS */;
INSERT INTO `order_items` VALUES
(1,2,1,'3965895793893','Mackerel','0.5 kg',NULL,1,0,1.00,100.00,0,0.00,0.00,0.00,100.00,'[[2,\"26-06-2025 07:46:10pm\"]]','2',NULL,NULL,1,0,'2025-06-26 19:46:10','2025-06-26 19:46:10',NULL),
(2,1,2,'10008417819524','Mackerel','0.5 kg',NULL,1,0,1.00,100.00,0,0.00,0.00,0.00,100.00,'[[2,\"26-06-2025 07:53:54pm\"]]','2',NULL,NULL,1,0,'2025-06-26 19:53:54','2025-06-26 19:53:54',NULL),
(3,1,3,'6378982628764','Mackerel','0.5 kg',NULL,1,0,6.00,100.00,0,0.00,0.00,0.00,600.00,'[[2,\"27-06-2025 05:13:23pm\"]]','2',NULL,NULL,1,0,'2025-06-27 17:13:23','2025-06-27 17:13:23',NULL),
(4,2,4,'6401774182507','karimeen','1 kg',NULL,3,0,2.00,400.00,0,0.00,0.00,0.00,800.00,'[[2,\"27-06-2025 07:22:05pm\"]]','2',NULL,NULL,1,0,'2025-06-27 19:22:05','2025-06-27 19:22:05',NULL),
(8,1,8,'3314706714272','Mackerel','0.5 kg',NULL,1,0,1.00,10.00,0,0.00,0.00,0.00,10.00,'[[1,\"27-06-2025 07:44:17pm\"]]','1',NULL,NULL,1,0,'2025-06-27 19:44:17','2025-06-27 19:44:17',NULL),
(9,1,9,'2034702153240','Mackerel','0.5 kg',NULL,1,0,5.00,10.00,0,0.00,0.00,0.00,50.00,'[[2,\"27-06-2025 07:59:15pm\"]]','2',NULL,NULL,1,0,'2025-06-27 19:59:15','2025-06-27 19:59:15',NULL),
(10,1,9,'2034702153240','karimeen','2 kg',NULL,5,0,2.00,800.00,0,0.00,0.00,0.00,1600.00,'[[2,\"27-06-2025 07:59:15pm\"]]','2',NULL,NULL,1,0,'2025-06-27 19:59:15','2025-06-27 19:59:15',NULL),
(11,1,10,'5732887213090','Mackerel','0.5 kg',NULL,1,0,5.00,10.00,0,0.00,0.00,0.00,50.00,'[[2,\"27-06-2025 07:59:39pm\"]]','2',NULL,NULL,1,0,'2025-06-27 19:59:39','2025-06-27 19:59:39',NULL),
(12,1,11,'13900959110989','karimeen','2 kg',NULL,5,0,1.00,800.00,0,0.00,0.00,0.00,800.00,'[[2,\"29-06-2025 03:37:03pm\"]]','2',NULL,NULL,1,0,'2025-06-29 15:37:03','2025-06-29 15:37:03',NULL),
(13,2,12,'8513236681626','karimeen','1 Gm',NULL,3,0,2.00,400.00,0,0.00,0.00,0.00,800.00,'[[2,\"05-07-2025 11:23:10am\"]]','2',NULL,NULL,1,0,'2025-07-05 11:23:10','2025-07-05 11:23:10',NULL),
(14,2,12,'8513236681626','karimeen','2 Gm',NULL,5,0,2.00,800.00,0,0.00,0.00,0.00,1600.00,'[[2,\"05-07-2025 11:23:10am\"]]','2',NULL,NULL,1,0,'2025-07-05 11:23:10','2025-07-05 11:23:10',NULL),
(15,1,13,'9942760959963','karimeen','0.5 Gm',NULL,2,0,2.00,100.00,70,0.00,0.00,0.00,140.00,'[[2,\"05-07-2025 06:18:09pm\"]]','2',NULL,NULL,1,0,'2025-07-05 18:18:09','2025-07-05 18:18:09',NULL),
(16,1,14,'2172402305905','Mackerel','0.5 kg',NULL,1,0,3.00,10.00,0,0.00,0.00,0.00,30.00,'[[2,\"08-07-2025 06:45:43am\"]]','2',NULL,NULL,1,0,'2025-07-08 06:45:43','2025-07-08 06:45:43',NULL),
(17,1,15,'5262868087375','karimeen','0.5 kg',NULL,2,0,1.00,100.00,70,0.00,0.00,0.00,70.00,'[[2,\"08-07-2025 11:13:44am\"]]','2',NULL,NULL,1,0,'2025-07-08 11:13:44','2025-07-08 11:13:44',NULL),
(18,3,16,'15973567990519','Mackerel','0.5 kg',NULL,1,0,6.00,10.00,0,0.00,0.00,0.00,60.00,'[[2,\"11-07-2025 11:20:51pm\"]]','2',NULL,NULL,1,0,'2025-07-11 23:20:51','2025-07-11 23:20:51',NULL),
(22,3,20,'12891865674043','Chicken fry cut','0.5 ',NULL,6,0,1.00,100.00,80,0.00,0.00,0.00,80.00,'[[2,\"12-07-2025 06:50:47pm\"]]','2',NULL,NULL,1,0,'2025-07-12 18:50:47','2025-07-12 18:50:47',NULL),
(23,1,21,'7221361176408','Mackerel','0.5 kg',NULL,1,0,1.00,10.00,0,0.00,0.00,0.00,10.00,'[[2,\"12-07-2025 08:27:23pm\"]]','2',NULL,NULL,1,0,'2025-07-12 20:27:23','2025-07-12 20:27:23',NULL),
(24,1,21,'7221361176408','Chicken fry cut','0.5 ',NULL,6,0,2.00,100.00,80,0.00,0.00,0.00,160.00,'[[2,\"12-07-2025 08:27:23pm\"]]','2',NULL,NULL,1,0,'2025-07-12 20:27:23','2025-07-12 20:27:23',NULL),
(25,1,22,'7564818911323','Chicken fry cut','0.5 ',NULL,6,0,2.00,100.00,80,0.00,0.00,0.00,160.00,'[[2,\"12-07-2025 08:29:46pm\"]]','2',NULL,NULL,1,0,'2025-07-12 20:29:46','2025-07-12 20:29:46',NULL),
(26,1,23,'12969294474508','Chicken fry cut','0.5 ',NULL,6,0,1.00,100.00,80,0.00,0.00,0.00,80.00,'[[2,\"13-07-2025 07:05:54am\"]]','2',NULL,NULL,1,0,'2025-07-13 07:05:54','2025-07-13 07:05:54',NULL),
(27,1,24,'6367209237977','Chicken fry cut','0.5 ',NULL,6,0,1.00,100.00,80,0.00,0.00,0.00,80.00,'[[2,\"21-07-2025 01:54:03pm\"]]','2',NULL,NULL,1,0,'2025-07-21 13:54:03','2025-07-21 13:54:03',NULL),
(28,1,25,'16621228559675','Chicken fry cut','0.5 ',NULL,6,0,1.00,100.00,80,0.00,0.00,0.00,80.00,'[[2,\"21-07-2025 08:17:04pm\"]]','2',NULL,NULL,1,0,'2025-07-21 20:17:04','2025-07-21 20:17:04',NULL),
(29,1,26,'8733014425648','Chicken fry cut','0.5 ',NULL,6,0,2.00,100.00,80,0.00,0.00,0.00,120.00,'[[2,\"23-07-2025 03:38:13pm\"]]','2',NULL,NULL,1,0,'2025-07-23 15:38:13','2025-07-23 15:38:13',NULL),
(30,1,27,'6669462893896','Chicken fry cut','0.5 ',NULL,6,0,2.00,100.00,80,0.00,0.00,0.00,120.00,'[[2,\"23-07-2025 06:41:00pm\"]]','2',NULL,NULL,1,0,'2025-07-23 18:41:00','2025-07-23 18:41:00',NULL),
(31,1,28,'8706770625358','Mackerel','0.5 kg',NULL,1,0,2.00,10.00,0,0.00,0.00,0.00,15.00,'[[2,\"23-07-2025 06:43:24pm\"]]','2',NULL,NULL,1,0,'2025-07-23 18:43:24','2025-07-23 18:43:24',NULL),
(32,1,29,'2920958730337','Chicken fry cut','0.5 ',NULL,6,0,3.00,100.00,80,0.00,0.00,0.00,200.00,'[[2,\"23-07-2025 06:45:47pm\"]]','2',NULL,NULL,1,0,'2025-07-23 18:45:48','2025-07-23 18:45:48',NULL),
(33,13,30,'13173533702250','Chicken fry cut','0.5 ','Curry Cut',6,0,1.50,100.00,80,0.00,0.00,0.00,120.00,'[[2,\"25-07-2025 01:54:38pm\"]]','2',NULL,NULL,1,0,'2025-07-25 13:54:38','2025-07-25 13:54:38',NULL),
(34,13,31,'3277164269716','karimeen','0.5 kg','Pickle Cut',2,0,0.50,100.00,70,0.00,0.00,0.00,35.00,'[[2,\"25-07-2025 01:55:26pm\"]]','2',NULL,NULL,1,0,'2025-07-25 13:55:26','2025-07-25 13:55:26',NULL),
(35,1,32,'7546791284993','karimeen','0.5 kg','Uncleaned',2,0,0.50,100.00,70,0.00,0.00,0.00,35.00,'[[2,\"25-07-2025 03:13:56pm\"]]','2',NULL,NULL,1,0,'2025-07-25 15:13:56','2025-07-25 15:13:56',NULL),
(36,1,33,'14704323240735','Chicken fry cut','0.5 ','Pickle Cut',6,0,1.50,100.00,80,0.00,0.00,0.00,120.00,'[[2,\"25-07-2025 03:21:06pm\"]]','2',NULL,NULL,1,0,'2025-07-25 15:21:06','2025-07-25 15:21:06',NULL),
(37,1,34,'2780951408475','Mackerel','0.5 kg','Curry Cut',1,0,2.50,10.00,0,0.00,0.00,0.00,25.00,'[[2,\"25-07-2025 03:23:25pm\"]]','2',NULL,NULL,1,0,'2025-07-25 15:23:25','2025-07-25 15:23:25',NULL),
(38,1,35,'16952231702797','Chicken fry cut','0.5 ','Uncleaned',6,0,3.50,100.00,80,0.00,0.00,0.00,280.00,'[[2,\"25-07-2025 03:24:48pm\"]]','2',NULL,NULL,1,0,'2025-07-25 15:24:48','2025-07-25 15:24:48',NULL),
(39,1,36,'13161301273912','Chicken fry cut','0.5 ','Pickle Cut',6,0,2.50,100.00,80,0.00,0.00,0.00,200.00,'[[2,\"25-07-2025 03:26:59pm\"]]','2',NULL,NULL,1,0,'2025-07-25 15:26:59','2025-07-25 15:26:59',NULL),
(40,1,37,'15047651878962','Mackerel','0.5 kg','Uncleaned',1,0,2.00,10.00,0,0.00,0.00,0.00,20.00,'[[2,\"27-07-2025 12:56:37pm\"]]','2',NULL,NULL,1,0,'2025-07-27 12:56:37','2025-07-27 12:56:37',NULL),
(41,1,38,'12096358909857','karimeen','0.5 kg','Uncleaned',2,0,1.50,100.00,70,0.00,0.00,0.00,105.00,'[[2,\"27-07-2025 01:39:45pm\"]]','4',NULL,NULL,1,0,'2025-07-27 13:39:45','2025-07-27 13:41:19',NULL),
(42,1,39,'8354171418218','Mackerel','0.5 kg','Fry Cut',1,0,1.00,10.00,0,0.00,0.00,0.00,10.00,'[[2,\"27-07-2025 01:50:11pm\"]]','2',NULL,NULL,1,0,'2025-07-27 13:50:11','2025-07-27 13:50:11',NULL),
(43,1,39,'8354171418218','karimeen','0.5 kg','Uncleaned',2,0,1.50,100.00,70,0.00,0.00,0.00,105.00,'[[2,\"27-07-2025 01:50:11pm\"]]','2',NULL,NULL,1,0,'2025-07-27 13:50:11','2025-07-27 13:50:11',NULL);
/*!40000 ALTER TABLE `order_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_status_lists`
--

DROP TABLE IF EXISTS `order_status_lists`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_status_lists` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `status` varchar(191) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_status_lists`
--

LOCK TABLES `order_status_lists` WRITE;
/*!40000 ALTER TABLE `order_status_lists` DISABLE KEYS */;
INSERT INTO `order_status_lists` VALUES
(1,'Payment Pending'),
(2,'Received'),
(3,'Processed'),
(4,'Shipped'),
(5,'Out For Delivery'),
(6,'Delivered'),
(7,'Cancelled'),
(8,'Returned');
/*!40000 ALTER TABLE `order_status_lists` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_statuses`
--

DROP TABLE IF EXISTS `order_statuses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_statuses` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` varchar(191) NOT NULL,
  `order_item_id` int(11) DEFAULT NULL,
  `status` varchar(191) NOT NULL,
  `created_by` int(11) NOT NULL COMMENT '0 - Script, if not 0 id of related table',
  `user_type` int(11) NOT NULL COMMENT '0 - Script, 1 - Admin, 2 - User',
  `created_at` timestamp NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_statuses`
--

LOCK TABLES `order_statuses` WRITE;
/*!40000 ALTER TABLE `order_statuses` DISABLE KEYS */;
INSERT INTO `order_statuses` VALUES
(1,'1',0,'2',2,2,'2025-06-26 19:46:10'),
(2,'2',0,'2',1,2,'2025-06-26 19:53:54'),
(3,'3',0,'2',1,2,'2025-06-27 17:13:23'),
(4,'4',0,'2',2,2,'2025-06-27 19:22:05'),
(5,'9',0,'2',1,2,'2025-06-27 19:59:15'),
(6,'10',0,'2',1,2,'2025-06-27 19:59:39'),
(7,'11',0,'2',1,2,'2025-06-29 15:37:03'),
(8,'12',0,'2',2,2,'2025-07-05 11:23:10'),
(9,'13',0,'2',1,2,'2025-07-05 18:18:09'),
(10,'14',0,'2',1,2,'2025-07-08 06:45:43'),
(11,'15',0,'2',1,2,'2025-07-08 11:13:44'),
(12,'16',0,'2',3,2,'2025-07-11 23:20:51'),
(13,'20',0,'2',3,2,'2025-07-12 18:50:47'),
(14,'21',0,'2',1,2,'2025-07-12 20:27:23'),
(15,'22',0,'2',1,2,'2025-07-12 20:29:46'),
(16,'23',0,'2',1,2,'2025-07-13 07:05:54'),
(17,'24',0,'2',1,2,'2025-07-21 13:54:03'),
(18,'25',0,'2',1,2,'2025-07-21 20:17:04'),
(19,'26',0,'2',1,2,'2025-07-23 15:38:13'),
(20,'27',0,'2',1,2,'2025-07-23 18:41:00'),
(21,'28',0,'2',1,2,'2025-07-23 18:43:24'),
(22,'29',0,'2',1,2,'2025-07-23 18:45:48'),
(23,'30',0,'2',13,2,'2025-07-25 13:54:38'),
(24,'31',0,'2',13,2,'2025-07-25 13:55:26'),
(25,'32',0,'2',1,2,'2025-07-25 15:13:56'),
(26,'33',0,'2',1,2,'2025-07-25 15:21:06'),
(27,'34',0,'2',1,2,'2025-07-25 15:23:25'),
(28,'35',0,'2',1,2,'2025-07-25 15:24:48'),
(29,'36',0,'2',1,2,'2025-07-25 15:26:59'),
(30,'37',0,'2',1,2,'2025-07-27 12:56:37'),
(31,'38',0,'2',1,2,'2025-07-27 13:39:45'),
(32,'38',0,'4',1,1,'2025-07-27 13:41:19'),
(33,'39',0,'2',1,2,'2025-07-27 13:50:11');
/*!40000 ALTER TABLE `order_statuses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `order_trackings`
--

DROP TABLE IF EXISTS `order_trackings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `order_trackings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `order_item_id` int(11) NOT NULL,
  `shiprocket_order_id` int(11) NOT NULL,
  `shipment_id` int(11) NOT NULL,
  `courier_company_id` int(11) DEFAULT NULL,
  `awb_code` varchar(191) DEFAULT NULL,
  `tracking_url` varchar(191) DEFAULT NULL,
  `pickup_status` int(11) NOT NULL,
  `pickup_scheduled_date` varchar(191) NOT NULL,
  `pickup_token_number` varchar(191) NOT NULL,
  `status` int(11) NOT NULL,
  `others` varchar(191) NOT NULL,
  `pickup_generated_date` varchar(191) NOT NULL,
  `data` varchar(191) NOT NULL,
  `date` varchar(191) NOT NULL,
  `is_canceled` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `order_trackings`
--

LOCK TABLES `order_trackings` WRITE;
/*!40000 ALTER TABLE `order_trackings` DISABLE KEYS */;
/*!40000 ALTER TABLE `order_trackings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `orders`
--

DROP TABLE IF EXISTS `orders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `delivery_boy_id` bigint(20) unsigned DEFAULT NULL,
  `delivery_boy_bonus_details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Delivery boy bonus Details for bonus commission amount' CHECK (json_valid(`delivery_boy_bonus_details`)),
  `delivery_boy_bonus_amount` double DEFAULT NULL,
  `transaction_id` bigint(20) unsigned DEFAULT NULL,
  `orders_id` varchar(191) DEFAULT NULL,
  `otp` int(11) DEFAULT NULL,
  `mobile` varchar(191) NOT NULL,
  `order_note` text DEFAULT NULL,
  `total` double(8,2) NOT NULL,
  `delivery_charge` double(8,2) NOT NULL,
  `tax_amount` double(8,2) NOT NULL DEFAULT 0.00,
  `tax_percentage` double(8,2) NOT NULL DEFAULT 0.00,
  `wallet_balance` double(8,2) NOT NULL,
  `discount` double(8,2) NOT NULL DEFAULT 0.00,
  `promo_code_id` int(11) NOT NULL DEFAULT 0,
  `promo_code` varchar(191) DEFAULT NULL,
  `promo_discount` double(8,2) NOT NULL DEFAULT 0.00,
  `final_total` double(8,2) DEFAULT NULL,
  `payment_method` varchar(191) NOT NULL,
  `address` text NOT NULL,
  `latitude` varchar(191) NOT NULL,
  `longitude` varchar(191) NOT NULL,
  `delivery_time` varchar(191) NOT NULL,
  `status` varchar(191) NOT NULL,
  `active_status` varchar(191) NOT NULL,
  `order_from` int(11) DEFAULT 0,
  `pincode_id` int(11) DEFAULT 0,
  `address_id` int(11) NOT NULL DEFAULT 0,
  `area_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `orders`
--

LOCK TABLES `orders` WRITE;
/*!40000 ALTER TABLE `orders` DISABLE KEYS */;
INSERT INTO `orders` VALUES
(1,2,0,NULL,NULL,0,'3965895793893',0,'9995286658','',100.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,100.00,'COD','12, Chandakunnu, Nilambur, Kerala 679329, India near smile smart dental care chandakunnu Nilambur Kerala India-682030 maaz 9995286658/9745506727','11.2855356','76.2385793','27-6-2025 Morning','[[2,\"26-06-2025 07:46:10pm\"]]','2',0,NULL,2,0,'2025-06-26 19:46:10','2025-06-26 19:46:10',NULL),
(2,1,0,NULL,NULL,0,'10008417819524',0,'9544794516','',100.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,100.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','27-6-2025 Morning','[[2,\"26-06-2025 07:53:54pm\"]]','2',0,NULL,1,0,'2025-06-26 19:53:54','2025-06-26 19:53:54',NULL),
(3,1,0,NULL,NULL,0,'6378982628764',0,'9544794516','',600.00,0.00,0.00,0.00,0.00,0.00,1,'New wel(50)',50.00,550.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','28-6-2025 Morning','[[2,\"27-06-2025 05:13:23pm\"]]','2',0,NULL,1,0,'2025-06-27 17:13:23','2025-06-27 17:13:23',NULL),
(4,2,0,NULL,NULL,0,'6401774182507',0,'9995286658','',800.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,800.00,'COD','12, Chandakunnu, Nilambur, Kerala 679329, India near smile smart dental care chandakunnu Nilambur Kerala India-682030 maaz 9995286658/9745506727','11.2855356','76.2385793','28-6-2025 Morning','[[2,\"27-06-2025 07:22:05pm\"]]','2',0,NULL,2,0,'2025-06-27 19:22:05','2025-06-27 19:22:05',NULL),
(5,2,0,NULL,NULL,0,'16170783759603',0,'9995286658','',600.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,600.00,'Razorpay','12, Chandakunnu, Nilambur, Kerala 679329, India near smile smart dental care chandakunnu Nilambur Kerala India-682030 maaz 9995286658/9745506727','11.2855356','76.2385793','28-6-2025 Morning','[[1,\"27-06-2025 07:22:30pm\"]]','1',0,NULL,2,0,'2025-06-27 19:22:30','2025-06-27 19:22:31','2025-06-27 19:22:31'),
(6,2,0,NULL,NULL,0,'7053158456188',0,'9995286658','',600.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,600.00,'Razorpay','12, Chandakunnu, Nilambur, Kerala 679329, India near smile smart dental care chandakunnu Nilambur Kerala India-682030 maaz 9995286658/9745506727','11.2855356','76.2385793','28-6-2025 Morning','[[1,\"27-06-2025 07:23:07pm\"]]','1',0,NULL,2,0,'2025-06-27 19:23:07','2025-06-27 19:23:08','2025-06-27 19:23:08'),
(7,2,0,NULL,NULL,0,'2871693261241',0,'9995286658','',600.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,600.00,'Razorpay','12, Chandakunnu, Nilambur, Kerala 679329, India near smile smart dental care chandakunnu Nilambur Kerala India-682030 maaz 9995286658/9745506727','11.2855356','76.2385793','28-6-2025 Morning','[[1,\"27-06-2025 07:24:36pm\"]]','1',0,NULL,2,0,'2025-06-27 19:24:36','2025-06-27 19:28:44','2025-06-27 19:28:44'),
(8,1,0,NULL,NULL,1,'3314706714272',0,'9544794516','',10.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,10.00,'Razorpay','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','28-6-2025 Morning','[[1,\"27-06-2025 07:44:17pm\"]]','2',0,NULL,1,0,'2025-06-27 19:44:17','2025-06-27 19:45:02',NULL),
(9,1,0,NULL,NULL,0,'2034702153240',909529,'9544794516','',1650.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,1650.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','28-6-2025 Morning','[[2,\"27-06-2025 07:59:15pm\"]]','2',0,NULL,1,0,'2025-06-27 19:59:15','2025-06-27 19:59:15',NULL),
(10,1,0,NULL,NULL,0,'5732887213090',650714,'9544794516','',50.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,80.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','28-6-2025 Morning','[[2,\"27-06-2025 07:59:39pm\"]]','2',0,NULL,1,0,'2025-06-27 19:59:39','2025-06-27 19:59:39',NULL),
(11,1,0,NULL,NULL,0,'13900959110989',874907,'9544794516','',800.00,0.00,0.00,0.00,50.00,0.00,0,'',0.00,750.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','29-6-2025 Morning','[[2,\"29-06-2025 03:37:03pm\"]]','2',0,NULL,1,0,'2025-06-29 15:37:03','2025-06-29 15:37:03',NULL),
(12,2,0,NULL,NULL,0,'8513236681626',836220,'9995286658','',2400.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,2400.00,'COD','12, Chandakunnu, Nilambur, Kerala 679329, India near smile smart dental care chandakunnu Nilambur Kerala India-682030 maaz 9995286658/9745506727','11.2855356','76.2385793','5-7-2025 Morning','[[2,\"05-07-2025 11:23:10am\"]]','2',0,NULL,2,0,'2025-07-05 11:23:10','2025-07-05 11:23:10',NULL),
(13,1,0,NULL,NULL,0,'9942760959963',314022,'9544794516','',140.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,170.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','5-7-2025 Morning','[[2,\"05-07-2025 06:18:09pm\"]]','2',0,NULL,1,0,'2025-07-05 18:18:09','2025-07-05 18:18:09',NULL),
(14,1,0,NULL,NULL,0,'2172402305905',888812,'9544794516','',30.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,60.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','8-7-2025 Morning','[[2,\"08-07-2025 06:45:43am\"]]','2',0,NULL,1,0,'2025-07-08 06:45:43','2025-07-08 06:45:43',NULL),
(15,1,0,NULL,NULL,0,'5262868087375',850198,'9544794516','',70.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,100.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','8-7-2025 Morning','[[2,\"08-07-2025 11:13:44am\"]]','2',0,NULL,1,0,'2025-07-08 11:13:44','2025-07-08 11:13:44',NULL),
(16,3,0,NULL,NULL,0,'15973567990519',217295,'9495037815','',60.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,90.00,'COD','12, Chandakunnu, Nilambur, Kerala 679329, India testadress test Nilambur Kerala India-543210 testuser 9495037815/','11.2855356','76.2385793','12-7-2025 Morning','[[2,\"11-07-2025 11:20:51pm\"]]','2',0,NULL,5,0,'2025-07-11 23:20:51','2025-07-11 23:20:51',NULL),
(17,3,0,NULL,NULL,0,'9868707334243',663467,'9495037815','',80.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,110.00,'Razorpay','12, Chandakunnu, Nilambur, Kerala 679329, India testadress test Nilambur Kerala India-543210 testuser 9495037815/','11.2855356','76.2385793','12-7-2025 Morning','[[1,\"11-07-2025 11:21:13pm\"]]','1',0,NULL,5,0,'2025-07-11 23:21:13','2025-07-11 23:21:49','2025-07-11 23:21:49'),
(18,3,0,NULL,NULL,0,'14212881849029',688384,'9495037815','',70.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,100.00,'Razorpay','12, Chandakunnu, Nilambur, Kerala 679329, India testadress test Nilambur Kerala India-543210 testuser 9495037815/','11.2855356','76.2385793','12-7-2025 Evening','[[1,\"12-07-2025 10:41:48am\"]]','1',0,NULL,5,0,'2025-07-12 10:41:48','2025-07-12 10:41:53','2025-07-12 10:41:53'),
(19,3,0,NULL,NULL,0,'11642268045209',988204,'9495037815','',70.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,100.00,'Razorpay','12, Chandakunnu, Nilambur, Kerala 679329, India testadress test Nilambur Kerala India-543210 testuser 9495037815/','11.2855356','76.2385793','12-7-2025 Morning','[[1,\"12-07-2025 10:57:00am\"]]','1',0,NULL,5,0,'2025-07-12 10:57:00','2025-07-12 10:57:26','2025-07-12 10:57:26'),
(20,3,0,NULL,NULL,0,'12891865674043',768863,'9495037815','',80.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,110.00,'COD','12, Chandakunnu, Nilambur, Kerala 679329, India testadress test Nilambur Kerala India-543210 testuser 9495037815/','11.2855356','76.2385793','12-7-2025 Morning','[[2,\"12-07-2025 06:50:47pm\"]]','2',0,NULL,5,0,'2025-07-12 18:50:47','2025-07-12 18:50:47',NULL),
(21,1,0,NULL,NULL,0,'7221361176408',593222,'9544794516','',170.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,200.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','13-7-2025 Morning','[[2,\"12-07-2025 08:27:23pm\"]]','2',0,NULL,1,0,'2025-07-12 20:27:23','2025-07-12 20:27:23',NULL),
(22,1,0,NULL,NULL,0,'7564818911323',847625,'9544794516','',160.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,190.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','13-7-2025 Morning','[[2,\"12-07-2025 08:29:46pm\"]]','2',0,NULL,1,0,'2025-07-12 20:29:46','2025-07-12 20:29:46',NULL),
(23,1,0,NULL,NULL,0,'12969294474508',659246,'9544794516','',80.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,110.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','13-7-2025 Morning','[[2,\"13-07-2025 07:05:54am\"]]','2',0,NULL,1,0,'2025-07-13 07:05:54','2025-07-13 07:05:54',NULL),
(24,1,0,NULL,NULL,0,'6367209237977',111780,'9544794516','',80.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,80.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','21-7-2025 Morning','[[2,\"21-07-2025 01:54:03pm\"]]','2',0,NULL,1,0,'2025-07-21 13:54:03','2025-07-21 13:54:03',NULL),
(25,1,0,NULL,NULL,0,'16621228559675',941596,'9544794516','',80.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,80.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','22-7-2025 Morning','[[2,\"21-07-2025 08:17:04pm\"]]','2',0,NULL,1,0,'2025-07-21 20:17:04','2025-07-21 20:17:04',NULL),
(26,1,0,NULL,NULL,0,'8733014425648',652840,'9544794516','',240.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,270.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','23-7-2025 Morning','[[2,\"23-07-2025 03:38:13pm\"]]','2',0,NULL,1,0,'2025-07-23 15:38:13','2025-07-23 15:38:13',NULL),
(27,1,0,NULL,NULL,0,'6669462893896',609622,'9544794516','',240.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,270.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','23-7-2025 Morning','[[2,\"23-07-2025 06:41:00pm\"]]','2',0,NULL,1,0,'2025-07-23 18:41:00','2025-07-23 18:41:00',NULL),
(28,1,0,NULL,NULL,0,'8706770625358',468407,'9544794516','',30.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,60.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','23-7-2025 Morning','[[2,\"23-07-2025 06:43:24pm\"]]','2',0,NULL,1,0,'2025-07-23 18:43:24','2025-07-23 18:43:24',NULL),
(29,1,0,NULL,NULL,0,'2920958730337',908642,'9544794516','',400.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,430.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','23-7-2025 Morning','[[2,\"23-07-2025 06:45:47pm\"]]','2',0,NULL,1,0,'2025-07-23 18:45:48','2025-07-23 18:45:48',NULL),
(30,13,0,NULL,NULL,0,'13173533702250',163634,'7012836907','',240.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,270.00,'COD','12, Chandakunnu, Nilambur, Kerala 679329, India landmark area Nilambur Kerala India-679329 Burhan 7012836907/1234567890','11.2855356','76.2385793','25-7-2025 Morning','[[2,\"25-07-2025 01:54:38pm\"]]','2',0,NULL,6,0,'2025-07-25 13:54:38','2025-07-25 13:54:38',NULL),
(31,13,0,NULL,NULL,0,'3277164269716',700647,'7012836907','',70.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,100.00,'COD','12, Chandakunnu, Nilambur, Kerala 679329, India landmark area Nilambur Kerala India-679329 Burhan 7012836907/1234567890','11.2855356','76.2385793','25-7-2025 Morning','[[2,\"25-07-2025 01:55:26pm\"]]','2',0,NULL,6,0,'2025-07-25 13:55:26','2025-07-25 13:55:26',NULL),
(32,1,0,NULL,NULL,0,'7546791284993',327964,'9544794516','',70.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,70.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','25-7-2025 Morning','[[2,\"25-07-2025 03:13:56pm\"]]','2',0,NULL,1,0,'2025-07-25 15:13:56','2025-07-25 15:13:56',NULL),
(33,1,0,NULL,NULL,0,'14704323240735',244784,'9544794516','',240.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,240.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','25-7-2025 Morning','[[2,\"25-07-2025 03:21:06pm\"]]','2',0,NULL,1,0,'2025-07-25 15:21:06','2025-07-25 15:21:06',NULL),
(34,1,0,NULL,NULL,0,'2780951408475',357993,'9544794516','',50.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,50.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','25-7-2025 Morning','[[2,\"25-07-2025 03:23:25pm\"]]','2',0,NULL,1,0,'2025-07-25 15:23:25','2025-07-25 15:23:25',NULL),
(35,1,0,NULL,NULL,0,'16952231702797',473393,'9544794516','',560.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,560.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','25-7-2025 Morning','[[2,\"25-07-2025 03:24:48pm\"]]','2',0,NULL,1,0,'2025-07-25 15:24:48','2025-07-25 15:24:48',NULL),
(36,1,0,NULL,NULL,0,'13161301273912',718982,'9544794516','',400.00,0.00,0.00,0.00,0.00,0.00,0,'',0.00,400.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','25-7-2025 Morning','[[2,\"25-07-2025 03:26:59pm\"]]','2',0,NULL,1,0,'2025-07-25 15:26:59','2025-07-25 15:26:59',NULL),
(37,1,0,NULL,NULL,0,'15047651878962',333278,'9544794516','',40.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,70.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','27-7-2025 Morning','[[2,\"27-07-2025 12:56:37pm\"]]','2',0,NULL,1,0,'2025-07-27 12:56:37','2025-07-27 12:56:37',NULL),
(38,1,0,NULL,NULL,0,'12096358909857',244602,'9544794516','',210.00,30.00,0.00,0.00,0.00,0.00,0,'',0.00,240.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','27-7-2025 Morning','[[2,\"27-07-2025 01:39:45pm\"]]','4',0,NULL,1,0,'2025-07-27 13:39:45','2025-07-27 13:41:19',NULL),
(39,1,0,NULL,NULL,0,'8354171418218',473076,'9544794516','',230.00,0.00,0.00,0.00,0.00,0.00,3,'Wlc100(30)',30.00,200.00,'COD','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India fairy land chandakunnu Nilambur Kerala India-679329 hurair 9544794516/7561079073','11.2863436','76.2406147','27-7-2025 Morning','[[2,\"27-07-2025 01:50:11pm\"]]','2',0,NULL,1,0,'2025-07-27 13:50:11','2025-07-27 13:50:11',NULL);
/*!40000 ALTER TABLE `orders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `panel_notifications`
--

DROP TABLE IF EXISTS `panel_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `panel_notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(191) NOT NULL,
  `notifiable_type` varchar(191) NOT NULL,
  `notifiable_id` bigint(20) unsigned NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `panel_notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `panel_notifications`
--

LOCK TABLES `panel_notifications` WRITE;
/*!40000 ALTER TABLE `panel_notifications` DISABLE KEYS */;
INSERT INTO `panel_notifications` VALUES
('00f35bb4-81f7-4811-8483-1b75bf60c43b','App\\Notifications\\OrderNotification','App\\Models\\Admin',2,'{\"type\":\"4\",\"order_id\":38,\"text\":\"Order  #38 has been Shipped\"}',NULL,'2025-07-27 13:41:19','2025-07-27 13:41:19'),
('09acd5fd-d472-465a-b516-b401b24b7f54','App\\Notifications\\OrderNotification','App\\Models\\Admin',1,'{\"type\":\"new_order\",\"order_id\":30,\"text\":\"You have Received new order  #30\"}',NULL,'2025-07-25 13:54:38','2025-07-25 13:54:38'),
('0c58f00c-46b8-4f1a-a46e-6a4a5b484d7e','App\\Notifications\\OrderNotification','App\\Models\\Admin',2,'{\"type\":\"new_order\",\"order_id\":32,\"text\":\"You have Received new order  #32\"}',NULL,'2025-07-25 15:13:56','2025-07-25 15:13:56'),
('18e7b4d0-72d0-484b-8b83-ed6c28e6f277','App\\Notifications\\OrderNotification','App\\Models\\Admin',1,'{\"type\":\"new_order\",\"order_id\":35,\"text\":\"You have Received new order  #35\"}',NULL,'2025-07-25 15:24:48','2025-07-25 15:24:48'),
('22ece2d5-288d-4054-9bd1-7ede1c268ea1','App\\Notifications\\OrderNotification','App\\Models\\Admin',1,'{\"type\":\"new_order\",\"order_id\":39,\"text\":\"You have Received new order  #39\"}',NULL,'2025-07-27 13:50:11','2025-07-27 13:50:11'),
('251b5c5c-d9bb-401f-a402-149083072292','App\\Notifications\\OrderNotification','App\\Models\\Admin',1,'{\"type\":\"4\",\"order_id\":38,\"text\":\"Order  #38 has been Shipped\"}',NULL,'2025-07-27 13:41:19','2025-07-27 13:41:19'),
('2f3427c3-263d-4e90-b5ee-24cd0be07562','App\\Notifications\\OrderNotification','App\\Models\\Admin',1,'{\"type\":\"new_order\",\"order_id\":32,\"text\":\"You have Received new order  #32\"}',NULL,'2025-07-25 15:13:56','2025-07-25 15:13:56'),
('2f77f498-295e-4fc5-9f1e-a2d048e46f86','App\\Notifications\\OrderNotification','App\\Models\\Admin',2,'{\"type\":\"new_order\",\"order_id\":31,\"text\":\"You have Received new order  #31\"}',NULL,'2025-07-25 13:55:26','2025-07-25 13:55:26'),
('3579198e-2cbb-4d21-a2a0-7c3d7509a9ce','App\\Notifications\\OrderNotification','App\\Models\\Admin',1,'{\"type\":\"new_order\",\"order_id\":38,\"text\":\"You have Received new order  #38\"}',NULL,'2025-07-27 13:39:45','2025-07-27 13:39:45'),
('3fa505b3-0bb2-4b1e-93c4-d27f5d842e06','App\\Notifications\\OrderNotification','App\\Models\\Admin',2,'{\"type\":\"new_order\",\"order_id\":33,\"text\":\"You have Received new order  #33\"}',NULL,'2025-07-25 15:21:06','2025-07-25 15:21:06'),
('6a98b99c-fe06-4c33-9629-8014e830464a','App\\Notifications\\OrderNotification','App\\Models\\Admin',2,'{\"type\":\"new_order\",\"order_id\":35,\"text\":\"You have Received new order  #35\"}',NULL,'2025-07-25 15:24:48','2025-07-25 15:24:48'),
('70ae150a-e287-44d4-b83d-247ef2877517','App\\Notifications\\OrderNotification','App\\Models\\Admin',1,'{\"type\":\"new_order\",\"order_id\":33,\"text\":\"You have Received new order  #33\"}',NULL,'2025-07-25 15:21:06','2025-07-25 15:21:06'),
('7b773142-91f4-4e0d-bcf0-b42c71891bc2','App\\Notifications\\OrderNotification','App\\Models\\Admin',1,'{\"type\":\"new_order\",\"order_id\":31,\"text\":\"You have Received new order  #31\"}',NULL,'2025-07-25 13:55:26','2025-07-25 13:55:26'),
('8424c01e-fff0-47a3-b343-c46d90923444','App\\Notifications\\OrderNotification','App\\Models\\Admin',2,'{\"type\":\"new_order\",\"order_id\":39,\"text\":\"You have Received new order  #39\"}',NULL,'2025-07-27 13:50:11','2025-07-27 13:50:11'),
('850a82d7-da93-4324-9eb5-1d152ef0f3f9','App\\Notifications\\OrderNotification','App\\Models\\Admin',2,'{\"type\":\"new_order\",\"order_id\":38,\"text\":\"You have Received new order  #38\"}',NULL,'2025-07-27 13:39:45','2025-07-27 13:39:45'),
('a0cde939-7ac4-4d6a-bb05-49c807e89839','App\\Notifications\\OrderNotification','App\\Models\\Admin',2,'{\"type\":\"new_order\",\"order_id\":34,\"text\":\"You have Received new order  #34\"}',NULL,'2025-07-25 15:23:25','2025-07-25 15:23:25'),
('a26eee95-5f1b-47b7-a4c2-0c50b7152886','App\\Notifications\\OrderNotification','App\\Models\\Admin',2,'{\"type\":\"new_order\",\"order_id\":30,\"text\":\"You have Received new order  #30\"}',NULL,'2025-07-25 13:54:38','2025-07-25 13:54:38'),
('a8959d26-4ec1-4963-ac81-3ccf3a6a0838','App\\Notifications\\OrderNotification','App\\Models\\Admin',2,'{\"type\":\"new_order\",\"order_id\":37,\"text\":\"You have Received new order  #37\"}',NULL,'2025-07-27 12:56:37','2025-07-27 12:56:37'),
('a9e40891-0454-4324-aaf1-7305d45cc93b','App\\Notifications\\OrderNotification','App\\Models\\Admin',1,'{\"type\":\"new_order\",\"order_id\":37,\"text\":\"You have Received new order  #37\"}',NULL,'2025-07-27 12:56:37','2025-07-27 12:56:37'),
('bce311fb-4e14-4107-9171-a05455302a9e','App\\Notifications\\OrderNotification','App\\Models\\Admin',1,'{\"type\":\"new_order\",\"order_id\":34,\"text\":\"You have Received new order  #34\"}',NULL,'2025-07-25 15:23:25','2025-07-25 15:23:25'),
('c4fb654f-10dc-4c94-a572-891185ed4c3d','App\\Notifications\\OrderNotification','App\\Models\\Admin',2,'{\"type\":\"new_order\",\"order_id\":36,\"text\":\"You have Received new order  #36\"}',NULL,'2025-07-25 15:26:59','2025-07-25 15:26:59'),
('db40ab89-9b01-4c51-a4e7-1fa80629e5ba','App\\Notifications\\OrderNotification','App\\Models\\Admin',1,'{\"type\":\"new_order\",\"order_id\":36,\"text\":\"You have Received new order  #36\"}',NULL,'2025-07-25 15:26:59','2025-07-25 15:26:59');
/*!40000 ALTER TABLE `panel_notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `password_resets`
--

DROP TABLE IF EXISTS `password_resets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `password_resets` (
  `email` varchar(191) NOT NULL,
  `token` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `password_resets`
--

LOCK TABLES `password_resets` WRITE;
/*!40000 ALTER TABLE `password_resets` DISABLE KEYS */;
/*!40000 ALTER TABLE `password_resets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_requests`
--

DROP TABLE IF EXISTS `payment_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payment_requests` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `payment_type` varchar(191) NOT NULL,
  `payment_address` varchar(191) NOT NULL,
  `amount_requested` int(11) NOT NULL,
  `remarks` varchar(191) NOT NULL,
  `status` tinyint(4) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_requests`
--

LOCK TABLES `payment_requests` WRITE;
/*!40000 ALTER TABLE `payment_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `payment_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payments`
--

DROP TABLE IF EXISTS `payments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `payments` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `txnid` varchar(191) NOT NULL,
  `payment_amount` decimal(8,2) NOT NULL,
  `payment_status` varchar(191) NOT NULL,
  `itemid` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payments`
--

LOCK TABLES `payments` WRITE;
/*!40000 ALTER TABLE `payments` DISABLE KEYS */;
/*!40000 ALTER TABLE `payments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permission_categories`
--

DROP TABLE IF EXISTS `permission_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permission_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `guard_name` varchar(191) NOT NULL DEFAULT 'web',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permission_categories`
--

LOCK TABLES `permission_categories` WRITE;
/*!40000 ALTER TABLE `permission_categories` DISABLE KEYS */;
INSERT INTO `permission_categories` VALUES
(1,'dashboard','web',NULL,NULL),
(2,'order','web',NULL,NULL),
(3,'category','web',NULL,NULL),
(4,'product','web',NULL,NULL),
(5,'seller','web',NULL,NULL),
(6,'home_slider_image','web',NULL,NULL),
(7,'new_offer_image','web',NULL,NULL),
(8,'promo_code','web',NULL,NULL),
(9,'return_request','web',NULL,NULL),
(10,'withdrawal_request','web',NULL,NULL),
(11,'delivery_boy','web',NULL,NULL),
(12,'send_notification','web',NULL,NULL),
(13,'system','web',NULL,NULL),
(14,'web_settings','web',NULL,NULL),
(15,'location','web',NULL,NULL),
(16,'featured_section','web',NULL,NULL),
(17,'customer','web',NULL,NULL),
(18,'report','web',NULL,NULL),
(19,'faq','web',NULL,NULL);
/*!40000 ALTER TABLE `permission_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `permissions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `guard_name` varchar(191) NOT NULL,
  `category_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES
(1,'manage_dashboard','web',1,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(2,'order_list','web',2,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(3,'order_update','web',2,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(4,'order_delete','web',2,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(5,'category_list','web',3,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(6,'category_create','web',3,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(7,'category_update','web',3,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(8,'category_delete','web',3,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(9,'manage_categories_order','web',3,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(10,'product_list','web',4,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(11,'product_create','web',4,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(12,'product_update','web',4,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(13,'product_delete','web',4,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(14,'manage_media','web',4,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(15,'manage_product_bulk_upload','web',4,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(16,'manage_product_order','web',4,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(17,'seller_list','web',5,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(18,'seller_create','web',5,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(19,'seller_update','web',5,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(20,'seller_delete','web',5,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(21,'home_slider_image_list','web',6,'2025-06-20 14:17:32','2025-06-20 14:17:32'),
(22,'home_slider_image_create','web',6,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(23,'home_slider_image_update','web',6,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(24,'home_slider_image_delete','web',6,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(25,'new_offer_image_list','web',7,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(26,'new_offer_image_create','web',7,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(27,'new_offer_image_update','web',7,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(28,'new_offer_image_delete','web',7,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(29,'promo_code_list','web',8,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(30,'promo_code_create','web',8,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(31,'promo_code_update','web',8,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(32,'promo_code_delete','web',8,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(33,'return_request_list','web',9,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(34,'return_request_update','web',9,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(35,'return_request_delete','web',9,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(36,'withdrawal_request_list','web',10,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(37,'withdrawal_request_update','web',10,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(38,'withdrawal_request_delete','web',10,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(39,'delivery_boy_list','web',11,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(40,'delivery_boy_create','web',11,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(41,'delivery_boy_update','web',11,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(42,'delivery_boy_delete','web',11,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(43,'fund_transfers_list','web',11,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(44,'fund_transfers_create','web',11,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(45,'cash_collection_list','web',11,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(46,'cash_collection_create','web',11,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(47,'notification_list','web',12,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(48,'notification_create','web',12,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(49,'notification_delete','web',12,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(50,'manage_time_slots','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(51,'time_slot_create','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(52,'time_slot_update','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(53,'time_slot_delete','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(54,'manage_store_settings','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(55,'manage_units','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(56,'unit_create','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(57,'unit_update','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(58,'manage_payment_methods','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(59,'manage_Notification_settings','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(60,'manage_contact_us','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(61,'manage_about_us','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(62,'manage_privacy_policy','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(63,'manage_privacy_policy_delivery_boy','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(64,'manage_privacy_policy_manager_app','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(65,'manage_privacy_policy_seller_app','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(66,'manage_secret_key','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(67,'manage_shipping_methods','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(68,'manage_system_registration','web',13,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(69,'general_settings','web',14,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(70,'manage_social_media_list','web',14,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(71,'manage_social_media_create','web',14,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(72,'manage_social_media_delete','web',14,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(73,'manage_social_media_update','web',14,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(74,'manage_about','web',14,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(75,'manage_policies','web',14,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(76,'city_list','web',15,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(77,'city_create','web',15,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(78,'city_update','web',15,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(79,'city_delete','web',15,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(80,'manage_deliverable_area','web',15,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(81,'featured_section_list','web',16,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(82,'featured_section_create','web',16,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(83,'featured_section_update','web',16,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(84,'featured_section_delete','web',16,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(85,'customer_list','web',17,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(86,'customer_update','web',17,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(87,'customer_delete','web',17,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(88,'manage_wishlists','web',17,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(89,'transaction_list','web',17,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(90,'manage_customer_wallet','web',17,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(91,'product_sales_reports','web',18,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(92,'sales_reports','web',18,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(93,'faq_list','web',19,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(94,'faq_create','web',19,'2025-06-20 14:17:33','2025-06-20 14:17:33'),
(95,'faq_update','web',19,'2025-06-20 14:17:33','2025-06-20 14:17:33');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `personal_access_tokens`
--

DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(191) NOT NULL,
  `tokenable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(191) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `personal_access_tokens`
--

LOCK TABLES `personal_access_tokens` WRITE;
/*!40000 ALTER TABLE `personal_access_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `personal_access_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pickup_locations`
--

DROP TABLE IF EXISTS `pickup_locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pickup_locations` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `seller_id` int(11) NOT NULL,
  `pickup_location` varchar(191) NOT NULL,
  `name` varchar(191) NOT NULL,
  `email` varchar(191) NOT NULL,
  `phone` varchar(191) NOT NULL,
  `address` text NOT NULL,
  `address_2` text NOT NULL,
  `city` varchar(191) NOT NULL,
  `state` varchar(191) NOT NULL,
  `country` varchar(191) NOT NULL,
  `pin_code` varchar(191) NOT NULL,
  `latitude` varchar(191) NOT NULL,
  `longitude` varchar(191) NOT NULL,
  `verified` tinyint(4) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pickup_locations`
--

LOCK TABLES `pickup_locations` WRITE;
/*!40000 ALTER TABLE `pickup_locations` DISABLE KEYS */;
/*!40000 ALTER TABLE `pickup_locations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pincodes`
--

DROP TABLE IF EXISTS `pincodes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `pincodes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `pincode` varchar(191) NOT NULL,
  `status` tinyint(4) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pincodes`
--

LOCK TABLES `pincodes` WRITE;
/*!40000 ALTER TABLE `pincodes` DISABLE KEYS */;
/*!40000 ALTER TABLE `pincodes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_images`
--

DROP TABLE IF EXISTS `product_images`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_images` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `product_variant_id` int(11) NOT NULL DEFAULT 0,
  `image` varchar(191) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_images`
--

LOCK TABLES `product_images` WRITE;
/*!40000 ALTER TABLE `product_images` DISABLE KEYS */;
INSERT INTO `product_images` VALUES
(1,2,0,'products/1751028441_67593.jpg'),
(2,24,0,'products/1753698111_22493.jpg');
/*!40000 ALTER TABLE `product_images` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_ratings`
--

DROP TABLE IF EXISTS `product_ratings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_ratings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `rate` tinyint(4) NOT NULL,
  `review` text NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `product_ratings_product_id_user_id_unique` (`product_id`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_ratings`
--

LOCK TABLES `product_ratings` WRITE;
/*!40000 ALTER TABLE `product_ratings` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_ratings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_tag`
--

DROP TABLE IF EXISTS `product_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_tag` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `product_id` bigint(20) unsigned NOT NULL,
  `tag_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `product_tag_product_id_tag_id_unique` (`product_id`,`tag_id`),
  KEY `product_tag_tag_id_foreign` (`tag_id`),
  CONSTRAINT `product_tag_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  CONSTRAINT `product_tag_tag_id_foreign` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_tag`
--

LOCK TABLES `product_tag` WRITE;
/*!40000 ALTER TABLE `product_tag` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_variants`
--

DROP TABLE IF EXISTS `product_variants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_variants` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `type` varchar(191) NOT NULL,
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '(1: Available, 0: Sold Out)',
  `measurement` double(8,2) NOT NULL,
  `price` double(11,2) NOT NULL,
  `discounted_price` double(11,2) NOT NULL DEFAULT 0.00,
  `stock` double(11,2) NOT NULL DEFAULT 0.00,
  `stock_unit_id` int(11) NOT NULL DEFAULT 0,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_variants`
--

LOCK TABLES `product_variants` WRITE;
/*!40000 ALTER TABLE `product_variants` DISABLE KEYS */;
INSERT INTO `product_variants` VALUES
(1,1,'loose',1,500.00,100.00,0.00,0.00,8,NULL),
(2,2,'loose',1,0.50,100.00,70.00,0.00,1,NULL),
(3,2,'loose',1,1.00,400.00,0.00,0.00,1,NULL),
(5,2,'loose',1,1.50,800.00,0.00,0.00,1,NULL),
(6,3,'loose',1,0.50,100.00,80.00,1.00,5,NULL),
(21,8,'loose',1,0.50,200.00,190.00,0.00,1,NULL),
(22,9,'loose',1,0.50,200.00,190.00,0.00,1,NULL),
(23,10,'loose',1,0.50,200.00,190.00,0.00,1,NULL),
(24,11,'loose',1,0.50,200.00,190.00,0.00,1,NULL),
(25,12,'loose',1,500.00,375.00,0.00,0.00,1,NULL),
(26,13,'loose',1,500.00,375.00,0.00,0.00,8,NULL),
(27,14,'loose',1,500.00,375.00,0.00,0.00,8,NULL),
(28,15,'loose',1,500.00,375.00,0.00,0.00,8,NULL),
(29,16,'loose',1,500.00,450.00,0.00,0.00,8,NULL),
(30,17,'loose',1,500.00,375.00,0.00,0.00,8,NULL),
(31,18,'loose',1,500.00,60.00,0.00,0.00,8,NULL),
(32,19,'loose',1,500.00,120.00,0.00,0.00,8,NULL),
(33,20,'loose',1,500.00,120.00,0.00,0.00,8,NULL),
(34,21,'loose',1,500.00,120.00,0.00,0.00,8,NULL),
(35,22,'loose',1,500.00,180.00,0.00,0.00,8,NULL),
(36,23,'loose',1,500.00,200.00,0.00,0.00,8,NULL),
(37,24,'loose',1,500.00,70.00,0.00,0.00,8,NULL),
(38,25,'loose',1,500.00,200.00,0.00,0.00,8,NULL),
(39,26,'loose',1,500.00,120.00,0.00,0.00,8,NULL),
(40,28,'loose',1,500.00,130.00,0.00,0.00,8,NULL),
(41,29,'loose',1,500.00,120.00,0.00,0.00,8,NULL),
(42,30,'loose',1,500.00,120.00,0.00,0.00,8,NULL),
(43,31,'loose',1,500.00,130.00,0.00,0.00,8,NULL),
(44,32,'loose',1,500.00,150.00,0.00,0.00,8,NULL),
(45,33,'loose',1,500.00,60.00,0.00,0.00,8,NULL),
(46,34,'packet',1,1.00,480.00,0.00,0.00,1,NULL),
(47,35,'loose',1,500.00,150.00,0.00,0.00,8,NULL),
(48,36,'packet',1,1.00,440.00,0.00,0.00,1,NULL),
(49,37,'packet',1,1.00,240.00,0.00,0.00,1,NULL),
(50,38,'packet',1,1.00,340.00,0.00,0.00,1,NULL),
(51,39,'packet',1,1.00,320.00,0.00,0.00,1,NULL),
(52,40,'packet',1,500.00,440.00,0.00,0.00,8,NULL),
(53,41,'packet',1,500.00,580.00,0.00,0.00,8,NULL),
(54,42,'packet',1,500.00,300.00,0.00,0.00,8,NULL),
(55,43,'packet',1,500.00,540.00,0.00,0.00,8,NULL),
(56,44,'packet',1,1.00,450.00,0.00,0.00,1,NULL),
(57,45,'packet',1,500.00,195.00,0.00,0.00,8,NULL),
(58,46,'packet',1,500.00,125.00,0.00,0.00,8,NULL),
(59,47,'packet',1,500.00,300.00,0.00,0.00,8,NULL),
(60,48,'packet',1,500.00,130.00,0.00,0.00,8,NULL),
(61,49,'packet',1,500.00,200.00,0.00,0.00,8,NULL),
(62,50,'packet',1,500.00,160.00,0.00,0.00,8,NULL),
(63,51,'packet',1,100.00,45.00,0.00,0.00,8,NULL),
(64,52,'packet',1,500.00,245.00,0.00,0.00,8,NULL),
(65,53,'packet',1,500.00,50.00,0.00,0.00,8,NULL),
(66,54,'packet',1,500.00,385.00,0.00,0.00,8,NULL),
(67,55,'packet',1,500.00,38.00,0.00,0.00,8,NULL),
(68,56,'packet',1,50.00,68.00,0.00,0.00,8,NULL),
(69,57,'packet',1,100.00,52.00,0.00,0.00,8,NULL),
(70,58,'packet',1,500.00,45.00,0.00,0.00,8,NULL),
(71,59,'packet',1,50.00,58.00,0.00,0.00,8,NULL),
(72,60,'packet',1,100.00,85.00,0.00,0.00,8,NULL),
(73,61,'packet',1,500.00,40.00,0.00,0.00,8,NULL),
(74,62,'packet',1,100.00,52.00,0.00,0.00,8,NULL),
(75,63,'packet',1,500.00,150.00,0.00,0.00,8,NULL),
(76,64,'packet',1,100.00,115.00,0.00,0.00,8,NULL),
(77,65,'packet',1,500.00,250.00,0.00,0.00,8,NULL),
(78,66,'loose',1,500.00,150.00,0.00,0.00,8,NULL),
(79,67,'loose',1,500.00,100.00,0.00,0.00,8,NULL),
(80,68,'loose',1,500.00,158.00,0.00,0.00,8,NULL),
(81,69,'loose',1,500.00,100.00,0.00,0.00,8,NULL),
(82,70,'loose',1,500.00,150.00,0.00,0.00,8,NULL),
(83,71,'loose',1,500.00,150.00,0.00,0.00,8,NULL),
(84,72,'loose',1,500.00,100.00,0.00,0.00,8,NULL),
(85,73,'loose',1,500.00,150.00,0.00,0.00,8,NULL),
(86,74,'loose',1,500.00,200.00,0.00,0.00,8,NULL),
(87,75,'loose',1,500.00,100.00,0.00,0.00,8,NULL),
(88,76,'loose',1,500.00,150.00,0.00,0.00,8,NULL),
(89,77,'loose',1,500.00,150.00,0.00,0.00,8,NULL),
(90,78,'loose',1,500.00,100.00,0.00,0.00,8,NULL),
(91,79,'loose',1,500.00,200.00,0.00,0.00,8,NULL),
(92,81,'loose',1,500.00,200.00,0.00,0.00,8,NULL),
(93,83,'loose',1,500.00,100.00,0.00,0.00,8,NULL),
(94,84,'loose',1,500.00,100.00,0.00,0.00,8,NULL);
/*!40000 ALTER TABLE `product_variants` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `products` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `seller_id` int(11) DEFAULT NULL,
  `row_order` int(11) NOT NULL,
  `name` varchar(191) NOT NULL,
  `tags` varchar(191) DEFAULT NULL,
  `tax_id` tinyint(4) DEFAULT 0,
  `brand_id` int(11) DEFAULT 0,
  `slug` varchar(191) NOT NULL,
  `category_id` int(11) NOT NULL,
  `indicator` tinyint(4) DEFAULT NULL COMMENT '0 - none | 1 - veg | 2 - non-veg',
  `manufacturer` varchar(191) DEFAULT NULL,
  `made_in` varchar(191) DEFAULT NULL,
  `return_status` tinyint(4) DEFAULT NULL,
  `cancelable_status` tinyint(4) DEFAULT NULL,
  `till_status` varchar(191) DEFAULT NULL,
  `image` text NOT NULL,
  `other_images` varchar(191) DEFAULT NULL,
  `description` text NOT NULL,
  `status` int(11) NOT NULL DEFAULT 0,
  `is_approved` int(11) DEFAULT NULL,
  `return_days` int(11) NOT NULL DEFAULT 0,
  `type` text DEFAULT NULL,
  `is_unlimited_stock` int(11) NOT NULL DEFAULT 0 COMMENT '0 = Limited & 1 = Unlimited',
  `cod_allowed` tinyint(4) NOT NULL,
  `total_allowed_quantity` int(11) NOT NULL,
  `tax_included_in_price` tinyint(4) NOT NULL DEFAULT 0,
  `fssai_lic_no` varchar(191) NOT NULL,
  `barcode` varchar(191) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=85 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
INSERT INTO `products` VALUES
(1,1,155,'Mackerel','',0,0,'Mackerel-5',1,NULL,'null','0',0,0,'null','products/1750430473_67175.jpg',NULL,'<p>test</p>',1,0,0,'loose',1,1,1000,0,'','','2025-06-20 14:41:13','2025-07-31 16:31:11',NULL),
(2,1,156,'karimeen','',0,0,'karimeen-1',1,NULL,'null','0',0,0,'2','products/1751028441_16376.jpg',NULL,'<p>karimeen 500 gram after cutting net weight appr:.350gram</p>',1,0,0,'loose',1,0,10,0,'','','2025-06-27 18:17:21','2025-07-31 16:31:53',NULL),
(8,1,20,'Beef uncut','',0,0,'Beef-uncut',4,2,NULL,'0',0,0,NULL,'products/1753541985_99306.jpg',NULL,'<p>Fresh beef Bonless(without bone )</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-26 20:29:45','2025-07-26 20:29:45',NULL),
(9,1,21,'Beef frycut','',0,0,'Beef-frycut',4,NULL,NULL,'0',0,0,NULL,'products/1753542151_93937.jpg',NULL,'<p>Fresh Beef boneless</p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-26 20:32:31','2025-07-26 20:32:31',NULL),
(10,1,22,'Beef Curry cut','',0,0,'Beef-Curry-cut',4,2,NULL,'0',0,0,NULL,'products/1753542315_70112.jpg',NULL,'<p>Fresh Beef Bonless</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-26 20:35:15','2025-07-26 20:35:15',NULL),
(11,1,23,'Beef Biriyani cut','',0,0,'Beef-Biriyani-cut',4,NULL,NULL,'0',0,0,NULL,'products/1753542398_9226.jpg',NULL,'<p>Fresh beef boneless</p>',1,1,0,'loose',1,0,10,0,'','','2025-07-26 20:36:38','2025-07-26 20:36:38',NULL),
(12,1,39,'Mutton Curry Cut','',0,0,'Mutton-Curry-Cut-1',3,2,'null','0',0,0,'null','products/1753617663_11998.jpg',NULL,'<p>Fresh mutton</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-27 17:31:03','2025-07-27 20:39:41',NULL),
(13,1,37,'Mutton Biriyani Cut','',0,0,'Mutton-Biriyani-Cut-1',3,2,'null','0',0,0,'null','products/1753617853_72113.jpg',NULL,'<p>Fresh mutton</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-27 17:34:13','2025-07-27 19:27:31',NULL),
(14,1,36,'Mutton Fry Cut','',0,0,'Mutton-Fry-Cut-1',3,2,'null','0',0,0,'null','products/1753618212_93885.jpg',NULL,'<p>Fresh mutton</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-27 17:40:12','2025-07-27 19:27:12',NULL),
(15,1,35,'Mutton Mandi Cut','',0,0,'Mutton-Mandi-Cut-1',3,2,'null','0',0,0,'null','products/1753619009_36110.jpg',NULL,'<p>Fresh mutton</p>',1,1,0,'loose',1,0,10,0,'','','2025-07-27 17:53:29','2025-07-27 19:26:49',NULL),
(16,1,34,'Mutton Leg','',0,0,'Mutton-Leg-1',3,2,'null','0',0,0,'null','products/1753624210_8341.jpg',NULL,'<p>Fresh mutton</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-27 17:58:45','2025-07-27 19:26:23',NULL),
(17,1,42,'MuttonBoneless','',0,0,'MuttonBoneless',3,2,'null','0',0,0,'null','products/1753624453_56595.jpg',NULL,'<p>Fresh Mutton</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-27 19:24:13','2025-07-28 14:48:12',NULL),
(18,1,75,'Chicken Parts','',0,0,'Chicken-Parts-1',2,NULL,'null','0',0,0,'null','products/1753694246_4781.jpg',NULL,'<p>Fresh Parts&nbsp;</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-28 14:47:26','2025-07-28 20:34:44',NULL),
(19,1,74,'Chicken Curry Cut','',0,0,'Chicken-Curry-Cut-1',2,NULL,'null','0',0,0,'null','products/1753694660_79452.jpeg',NULL,'<p>Fresh chicken</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-28 14:54:20','2025-07-28 20:34:11',NULL),
(20,1,73,'Chicken Biriyani Cut','',0,0,'Chicken-Biriyani-Cut-1',2,NULL,'null','0',0,0,'null','products/1753695611_15243.jpg',NULL,'<p>Fresh chicken</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-28 15:10:11','2025-07-28 20:33:40',NULL),
(21,1,72,'Chicken Chilly Cut','',0,0,'Chicken-Chilly-Cut-1',2,NULL,'null','0',0,0,'null','products/1753696369_81680.jpg',NULL,'<p>Fresh Chicken&nbsp;</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-28 15:22:49','2025-07-28 20:33:10',NULL),
(22,1,70,'Chicken Leg Pieces','',0,0,'Chicken-Leg-Pieces-1',2,2,'null','0',0,0,'null','products/1753696665_89983.jpg',NULL,'<p>Fresh Leg Pieces</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-28 15:27:45','2025-07-28 20:32:27',NULL),
(23,1,69,'Chicken Boneless','',0,0,'Chicken-Boneless-2',2,2,'null','0',0,0,'null','products/1753697777_55713.jpg',NULL,'<p>Fresh Chicken</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-28 15:46:17','2025-07-28 20:32:01',NULL),
(24,1,68,'Live Chicken','',0,0,'Live-Chicken-1',2,NULL,'null','0',0,0,'null','products/1753698111_82527.jpeg',NULL,'<p>Fresh Chicken&nbsp;</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-28 15:51:51','2025-07-28 20:31:38',NULL),
(25,1,67,'Chicken Boneless Finger Size','',0,0,'Chicken-Boneless-Finger-Size-1',2,NULL,'null','0',0,0,'null','products/1753698306_35119.jpeg',NULL,'<p>Fresh Chicken</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-28 15:55:06','2025-07-28 20:31:08',NULL),
(26,1,66,'Chicken 65 Cut','',0,0,'Chicken-65-Cut-1',2,NULL,'null','0',0,0,'null','products/1753698438_14444.jpeg',NULL,'<p>Fresh Chicken</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-28 15:57:18','2025-07-28 20:30:42',NULL),
(28,1,65,'Frozen Chicken With Skin','',0,0,'Frozen-Chicken-With-Skin-1',2,NULL,'null','0',0,0,'null','products/1753698933_75921.jpg',NULL,'<p>Fresh Chicken</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-28 16:05:33','2025-07-28 20:30:19',NULL),
(29,1,64,'Chicken Mandi Cut','',0,0,'Chicken-Mandi-Cut-1',2,NULL,'null','0',0,0,'null','products/1753699228_66684.jpeg',NULL,'<p>Fresh Chicken</p>',1,1,0,'loose',1,1,50,0,'','','2025-07-28 16:10:28','2025-07-28 20:29:27',NULL),
(30,1,76,'Chicken fry','',0,0,'Chicken-fry-1',2,NULL,NULL,'0',0,0,NULL,'products/1753715287_17644.jpg',NULL,'<p>Fresh chicken</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-28 20:38:07','2025-07-28 20:38:07',NULL),
(31,1,77,'Botty','',0,0,'Botty',4,NULL,NULL,'0',0,0,NULL,'products/1753775878_48279.jpg',NULL,'<p>Fresh botty</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-29 13:27:58','2025-07-29 13:27:58',NULL),
(32,1,78,'Liver','',0,0,'Liver',4,NULL,NULL,'0',0,0,NULL,'products/1753775943_25095.jpg',NULL,'<p>Fresh liver</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-29 13:29:03','2025-07-29 13:29:03',NULL),
(33,1,79,'Kappa kootu','',0,0,'Kappa-kootu',4,NULL,NULL,'0',0,0,NULL,'products/1753776124_40997.jpg',NULL,'<p>Fresh</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-29 13:32:04','2025-07-29 13:32:04',NULL),
(34,1,104,'Chicken Cheese Ball','',0,0,'Chicken-Cheese-Ball-1',14,2,'null','0',0,0,'null','products/1753776189_10557.jpg',NULL,'<p>Fresh Frozen Snack&nbsp;</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 13:33:09','2025-07-29 19:02:10',NULL),
(35,1,81,'Vennallu','',0,0,'Vennallu',4,NULL,NULL,'0',0,0,NULL,'products/1753776219_81610.jpg',NULL,'<p>Fresh</p>',1,1,0,'loose',1,1,10,0,'','','2025-07-29 13:33:39','2025-07-29 13:33:39',NULL),
(36,1,105,'Chicken Popcorn (Breaded)','',0,0,'Chicken-Popcorn-(Breaded)-1',14,2,'null','0',0,0,'null','products/1753780727_9428.jpg',NULL,'<p>Fresh Frozen Snacks</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 14:48:47','2025-07-29 19:02:39',NULL),
(37,1,99,'Chicken Momos','',0,0,'Chicken-Momos-1',14,2,'null','0',0,0,'null','products/1753782332_25958.jpg',NULL,'<p>Fresh Frozen Snacks</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 15:15:32','2025-07-29 19:00:13',NULL),
(38,1,100,'Chicken Burger Patty','',0,0,'Chicken-Burger-Patty-1',14,2,'null','0',0,0,'null','products/1753782635_38481.jpg',NULL,'<p>Frozen Chicken Burger Patty</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 15:20:35','2025-07-29 19:00:33',NULL),
(39,1,101,'Burger Patty(veg)','',0,0,'Burger-Patty(veg)-1',14,1,'null','0',0,0,'null','products/1753782862_33909.jpg',NULL,'<p>Vegitable Burger Patty</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 15:24:22','2025-07-29 19:00:53',NULL),
(40,1,113,'Chicken Nuggets Breaded','',0,0,'Chicken-Nuggets-Breaded-1',14,2,'null','0',0,0,'null','products/1753783033_53078.jpg',NULL,'<p>Frozen Chicken Nuggets&nbsp;</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 15:27:13','2025-07-30 13:21:56',NULL),
(41,1,114,'Chicken Lollipop Breaded','',0,0,'Chicken-Lollipop-Breaded-1',14,2,'null','0',0,0,'null','products/1753783815_49768.jpg',NULL,'<p>Frozen Chicken Lollipop</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 15:40:15','2025-07-30 13:24:13',NULL),
(42,1,115,'French Fries','',0,0,'French-Fries-1',14,1,'null','0',0,0,'null','products/1753784064_58076.jpg',NULL,'<p>Frozen French Frie</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 15:44:24','2025-07-30 13:24:59',NULL),
(43,1,116,'Chicken Finger Breaded','',0,0,'Chicken-Finger-Breaded-1',14,2,'null','0',0,0,'null','products/1753784268_49673.jpg',NULL,'<p>Frozen Chicken Finger</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 15:47:48','2025-07-30 13:26:01',NULL),
(44,1,96,'Chicken Samosa','',0,0,'Chicken-Samosa-1',14,2,'null','0',0,0,'null','products/1753784482_81860.jpg',NULL,'<p>Frozen Samosa</p>',1,1,0,'packet',1,1,20,0,'','','2025-07-29 15:51:22','2025-07-29 18:58:30',NULL),
(45,1,112,'Red Chilly Powder','',0,0,'Red-Chilly-Powder-1',9,1,'null','0',0,0,'null','products/1753787272_3643.jpg',NULL,'<p>Fresh chilly Powder</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 16:37:52','2025-07-30 13:20:52',NULL),
(46,1,111,'Coriander Powder','',0,0,'Coriander-Powder-1',9,1,'null','0',0,0,'null','products/1753787434_99091.jpg',NULL,'<p>Fresh Coriander Powder</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 16:40:34','2025-07-30 13:19:59',NULL),
(47,1,106,'Prawns(1kg packet)','',0,0,'Prawns(1kg-packet)',8,NULL,NULL,'0',0,0,NULL,'products/1753798817_23507.png',NULL,'<p>Frozen pealed prawns only one kg packet</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 19:50:17','2025-07-29 19:50:17',NULL),
(48,1,109,'Whole Chicken with skin(1kg packet)','',0,0,'Whole-Chicken-with-skin(1kg-packet)-1',8,NULL,'null','0',0,0,'null','products/1753799029_1200.jpg',NULL,'<p>Frozen</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 19:53:49','2025-07-29 19:57:11',NULL),
(49,1,110,'Basa fish(1kg packet)','',0,0,'Basa-fish(1kg-packet)',8,NULL,NULL,'0',0,0,NULL,'products/1753799890_61362.jpg',NULL,'<p>Frozen</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-29 20:08:10','2025-07-29 20:08:10',NULL),
(50,1,117,'Turmeric Powder','',0,0,'Turmeric-Powder',9,1,NULL,'0',0,0,NULL,'products/1753862305_39349.jpg',NULL,'<p>Fresh Curry Powder</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 13:28:25','2025-07-30 13:28:25',NULL),
(51,1,118,'Fennel Powder','',0,0,'Fennel-Powder',9,1,NULL,'0',0,0,NULL,'products/1753862474_8740.jpg',NULL,'<p>Fresh Curry Powder</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 13:31:14','2025-07-30 13:31:14',NULL),
(52,1,119,'Mother\'s Health Mix','',0,0,'Mother\'s-Health-Mix',9,1,NULL,'0',0,0,NULL,'products/1753862718_7590.jpg',NULL,'<p>Healthy</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 13:35:18','2025-07-30 13:35:18',NULL),
(53,1,126,'Ragi Flour','',0,0,'Ragi-Flour',9,1,'null','0',0,0,'null','products/1753868555_95825.jpg',NULL,'<p>Healthy</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 13:37:04','2025-07-30 15:12:35',NULL),
(54,1,121,'Kashmiri Chilly Powder','',0,0,'Kashmiri-Chilly-Powder',9,1,NULL,'0',0,0,NULL,'products/1753867851_99433.jpg',NULL,'<p>Fresh Kashmiri Chilly</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 15:00:51','2025-07-30 15:00:51',NULL),
(55,1,125,'Rice Flour','',0,0,'Rice-Flour',9,1,'null','0',0,0,'null','products/1753868086_56510.jpg',NULL,'<p>Fresh Rice Flour</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 15:04:46','2025-07-30 15:10:28',NULL),
(56,1,123,'Biriyani Masala','',0,0,'Biriyani-Masala',9,1,NULL,'0',0,0,NULL,'products/1753868210_62636.jpg',NULL,'<p>Fresh Masala</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 15:06:50','2025-07-30 15:06:50',NULL),
(57,1,124,'Chicken Masala','',0,0,'Chicken-Masala',9,1,NULL,'0',0,0,NULL,'products/1753868337_28096.jpg',NULL,'<p>Fresh Masala</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 15:08:57','2025-07-30 15:08:57',NULL),
(58,1,127,'Puttu Podi','',0,0,'Puttu-Podi',9,1,NULL,'0',0,0,NULL,'products/1753868720_59587.jpg',NULL,'<p>Best Break Fast Recipe&nbsp;</p>',1,1,0,'packet',1,1,20,0,'','','2025-07-30 15:15:20','2025-07-30 15:15:20',NULL),
(59,1,128,'Garam Masala','',0,0,'Garam-Masala',9,1,NULL,'0',0,0,NULL,'products/1753868868_36171.jpg',NULL,'<p>Fresh Garam Masala</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 15:17:48','2025-07-30 15:17:48',NULL),
(60,1,129,'Kabsa Masala','',0,0,'Kabsa-Masala',9,1,NULL,'0',0,0,NULL,'products/1753869094_50451.jpg',NULL,'<p>Fresh Kabsa Masala</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 15:21:34','2025-07-30 15:21:34',NULL),
(61,1,130,'Whole Wheat Flour','',0,0,'Whole-Wheat-Flour',9,1,NULL,'0',0,0,NULL,'products/1753869352_80850.jpg',NULL,'<p>Fresh Wheat Flour</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 15:25:52','2025-07-30 15:25:52',NULL),
(62,1,131,'Meat Masala','',0,0,'Meat-Masala',9,2,NULL,'0',0,0,NULL,'products/1753869525_43483.jpg',NULL,'<p>Fresh Meat Masala</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 15:28:45','2025-07-30 15:28:45',NULL),
(63,1,132,'Navara Rice Powder','',0,0,'Navara-Rice-Powder',9,1,NULL,'0',0,0,NULL,'products/1753869657_88510.jpg',NULL,'<p>Fresh Navara Rice</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 15:30:57','2025-07-30 15:30:57',NULL),
(64,1,133,'Pepper Powder','',0,0,'Pepper-Powder',9,1,NULL,'0',0,0,NULL,'products/1753869783_40879.jpg',NULL,'<p>Fresh Pepper Powder</p>',1,1,0,'packet',1,1,10,0,'','','2025-07-30 15:33:03','2025-07-30 15:33:03',NULL),
(65,1,134,'Banana Powder','',0,0,'Banana-Powder',9,1,NULL,'0',0,0,NULL,'products/1753869992_96283.jpg',NULL,'<p>Healthy Baby Food</p>',1,1,0,'packet',1,0,10,0,'','','2025-07-30 15:36:32','2025-07-30 15:36:32',NULL),
(66,1,135,'Sardine/മത്തി','',0,0,'Sardineമത്തി',1,2,NULL,'0',0,0,NULL,'products/1753949339_14311.jpeg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,0,20,0,'','','2025-07-31 13:38:59','2025-07-31 13:38:59',NULL),
(67,1,146,'Sardine  Small/ കുഞ്ഞൻ മത്തി(uncleaned)','',0,0,'Sardine-Small-കുഞ്ഞൻ-മത്തി(uncleaned)',1,2,'null','0',0,0,'null','products/1753949527_10915.jpg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 13:42:07','2025-07-31 15:46:21',NULL),
(68,1,137,'Sardine (ഉണ്ടമത്തി)','',0,0,'Sardine-(ഉണ്ടമത്തി)',1,2,NULL,'0',0,0,NULL,'products/1753953934_94127.png',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,0,20,0,'','','2025-07-31 14:55:34','2025-07-31 14:55:34',NULL),
(69,1,138,'Mackerel/അയല','',0,0,'Mackerelഅയല',1,2,NULL,'0',0,0,NULL,'products/1753954295_99907.png',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 15:01:35','2025-07-31 15:01:35',NULL),
(70,1,139,'Mackerel/നാടൻ അയല','',0,0,'Mackerelനാടൻ-അയല',1,2,NULL,'0',0,0,NULL,'products/1753954442_12424.jpg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 15:04:02','2025-07-31 15:04:02',NULL),
(71,1,140,'Sardine/നാടൻ മത്തി','',0,0,'Sardineനാടൻ-മത്തി',1,2,NULL,'0',0,0,NULL,'products/1753954806_10534.jpg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 15:10:06','2025-07-31 15:10:06',NULL),
(72,1,141,'Mackerel/അയല മീഡിയം','',0,0,'Mackerelഅയല-മീഡിയം',1,2,NULL,'0',0,0,NULL,'products/1753955050_43952.jpg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 15:14:11','2025-07-31 15:14:11',NULL),
(73,1,142,'Mackerel Big/അയല വലുത്','',0,0,'Mackerel-Bigഅയല-വലുത്',1,2,NULL,'0',0,0,NULL,'products/1753955275_93173.jpg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 15:17:55','2025-07-31 15:17:55',NULL),
(74,1,145,'Anchovy Small/ബത്തൽ ചെറുത്(uncleaned)','',0,0,'Anchovy-Smallബത്തൽ-ചെറുത്(uncleaned)',9,2,'null','0',0,0,'null','products/1753956904_88614.png',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 15:43:25','2025-07-31 15:45:50',NULL),
(75,1,147,'Sardine/മത്തി മീഡിയം','',0,0,'Sardineമത്തി-മീഡിയം',1,2,NULL,'0',0,0,NULL,'products/1753957499_53707.jpg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 15:54:59','2025-07-31 15:54:59',NULL),
(76,1,148,'Anchovy/ബത്തൽ വലുത്','',0,0,'Anchovyബത്തൽ-വലുത്',1,2,NULL,'0',0,0,NULL,'products/1753957634_13704.jpg',NULL,'<p>Fresh Fish&nbsp;</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 15:57:14','2025-07-31 15:57:14',NULL),
(77,1,149,'Pearl Spot/കരിമീൻ','',0,0,'Pearl-Spotകരിമീൻ',1,2,NULL,'0',0,0,NULL,'products/1753957772_53956.jpg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 15:59:32','2025-07-31 15:59:32',NULL),
(78,1,150,'Pink Perch/കിളി മീൻ','',0,0,'Pink-Perchകിളി-മീൻ',1,2,NULL,'0',0,0,NULL,'products/1753958029_46386.jpg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 16:03:49','2025-07-31 16:03:49',NULL),
(79,1,151,'Pearl Spot/കരിമീൻ വലുത്','',0,0,'Pearl-Spotകരിമീൻ-വലുത്',1,2,NULL,'0',0,0,NULL,'products/1753958380_76663.jpg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 16:09:40','2025-07-31 16:09:40',NULL),
(81,1,152,'Pink Perch/കിളി മീൻ വലുത്','',0,0,'Pink-Perchകിളി-മീൻ-വലുത്',1,2,NULL,'0',0,0,NULL,'products/1753958682_87223.png',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 16:14:42','2025-07-31 16:14:42',NULL),
(83,1,153,'Pink Perch/കിളി മീൻ ചെറുത്(uncleaned)','',0,0,'Pink-Perchകിളി-മീൻ-ചെറുത്(uncleaned)',1,2,NULL,'0',0,0,NULL,'products/1753958873_67570.jpeg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 16:17:53','2025-07-31 16:17:53',NULL),
(84,1,154,'White Sardine/വെളൂരി(uncleaned)','',0,0,'White-Sardineവെളൂരി(uncleaned)',1,2,NULL,'0',0,0,NULL,'products/1753959194_81661.jpg',NULL,'<p>Fresh Fish</p>',1,1,0,'loose',1,1,20,0,'','','2025-07-31 16:23:14','2025-07-31 16:23:14',NULL);
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `promo_codes`
--

DROP TABLE IF EXISTS `promo_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `promo_codes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `promo_code` varchar(191) NOT NULL,
  `message` varchar(191) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `no_of_users` int(11) NOT NULL,
  `minimum_order_amount` int(11) NOT NULL,
  `discount` int(11) NOT NULL,
  `discount_type` varchar(191) NOT NULL,
  `max_discount_amount` int(11) NOT NULL,
  `repeat_usage` tinyint(4) NOT NULL COMMENT '1-allowed, 0-Not Allowed',
  `no_of_repeat_usage` int(11) NOT NULL DEFAULT 0 COMMENT 'if repeat_usage = allowed(1) else NULL',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1-active, 0-deactive',
  `image` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `promo_codes`
--

LOCK TABLES `promo_codes` WRITE;
/*!40000 ALTER TABLE `promo_codes` DISABLE KEYS */;
INSERT INTO `promo_codes` VALUES
(1,'New wel','Welcon','2025-06-27','2025-06-28',10,500,50,'amount',0,0,0,1,'promocode/1751024478_32328.jpg','2025-06-27 17:11:18','2025-06-27 17:11:18'),
(2,'Fresh','New','2025-06-29','2025-06-29',10,50,50,'amount',0,0,0,1,'promocode/1751198391_48132.jpg','2025-06-29 17:29:51','2025-06-29 17:29:51'),
(3,'Wlc100','Welcom','2025-07-27','2025-07-31',10,100,30,'amount',0,0,0,1,'promocode/1753604296_93666.png','2025-07-27 13:48:16','2025-07-27 13:48:16');
/*!40000 ALTER TABLE `promo_codes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rating_images`
--

DROP TABLE IF EXISTS `rating_images`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `rating_images` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `product_rating_id` int(11) NOT NULL,
  `image` varchar(191) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rating_images`
--

LOCK TABLES `rating_images` WRITE;
/*!40000 ALTER TABLE `rating_images` DISABLE KEYS */;
/*!40000 ALTER TABLE `rating_images` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `return_requests`
--

DROP TABLE IF EXISTS `return_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `return_requests` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `product_variant_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `order_item_id` int(11) NOT NULL,
  `reason` text NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 0,
  `remarks` text DEFAULT NULL,
  `delivery_boy_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `return_requests_order_item_id_unique` (`order_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `return_requests`
--

LOCK TABLES `return_requests` WRITE;
/*!40000 ALTER TABLE `return_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `return_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_has_permissions`
--

DROP TABLE IF EXISTS `role_has_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) unsigned NOT NULL,
  `role_id` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`),
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_has_permissions`
--

LOCK TABLES `role_has_permissions` WRITE;
/*!40000 ALTER TABLE `role_has_permissions` DISABLE KEYS */;
INSERT INTO `role_has_permissions` VALUES
(1,1),
(1,2),
(1,3),
(1,4),
(2,1),
(2,2),
(2,3),
(2,4),
(3,1),
(3,2),
(3,3),
(3,4),
(4,1),
(4,2),
(4,3),
(4,4),
(5,1),
(5,2),
(5,3),
(6,1),
(6,2),
(7,1),
(7,2),
(8,1),
(8,2),
(9,1),
(9,2),
(10,1),
(10,2),
(10,3),
(11,1),
(11,2),
(11,3),
(12,1),
(12,2),
(12,3),
(13,1),
(13,2),
(13,3),
(14,1),
(14,2),
(14,3),
(15,1),
(15,2),
(15,3),
(16,1),
(16,2),
(17,1),
(17,2),
(18,1),
(18,2),
(19,1),
(19,2),
(20,1),
(20,2),
(21,1),
(21,2),
(22,1),
(22,2),
(23,1),
(23,2),
(24,1),
(24,2),
(25,1),
(25,2),
(26,1),
(26,2),
(27,1),
(27,2),
(28,1),
(28,2),
(29,1),
(29,2),
(30,1),
(30,2),
(31,1),
(31,2),
(32,1),
(32,2),
(33,1),
(33,2),
(33,3),
(34,1),
(34,2),
(34,3),
(35,1),
(35,2),
(35,3),
(36,1),
(36,2),
(37,1),
(37,2),
(38,1),
(38,2),
(39,1),
(39,2),
(40,1),
(40,2),
(41,1),
(41,2),
(42,1),
(42,2),
(43,1),
(43,2),
(44,1),
(44,2),
(45,1),
(45,2),
(46,1),
(46,2),
(47,1),
(47,2),
(48,1),
(48,2),
(49,1),
(49,2),
(50,1),
(50,2),
(51,1),
(51,2),
(52,1),
(52,2),
(53,1),
(53,2),
(54,1),
(54,2),
(55,1),
(55,2),
(56,1),
(56,2),
(57,1),
(57,2),
(58,1),
(58,2),
(59,1),
(59,2),
(60,1),
(60,2),
(61,1),
(61,2),
(62,1),
(62,2),
(63,1),
(63,2),
(64,1),
(64,2),
(65,1),
(65,2),
(66,1),
(66,2),
(67,1),
(67,2),
(68,1),
(68,2),
(69,1),
(69,2),
(70,1),
(70,2),
(71,1),
(71,2),
(72,1),
(72,2),
(73,1),
(73,2),
(74,1),
(74,2),
(75,1),
(75,2),
(76,1),
(76,2),
(77,1),
(77,2),
(78,1),
(78,2),
(79,1),
(79,2),
(80,1),
(80,2),
(81,1),
(81,2),
(82,1),
(82,2),
(83,1),
(83,2),
(84,1),
(84,2),
(85,1),
(85,2),
(86,1),
(86,2),
(87,1),
(87,2),
(88,1),
(88,2),
(89,1),
(89,2),
(90,1),
(90,2),
(91,1),
(91,2),
(91,3),
(91,4),
(92,1),
(92,2),
(92,3),
(92,4),
(93,1),
(93,2),
(94,1),
(94,2),
(95,1),
(95,2);
/*!40000 ALTER TABLE `role_has_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `roles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `guard_name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES
(1,'Super Admin','web',NULL,NULL),
(2,'Admin','web',NULL,NULL),
(3,'Seller','web',NULL,NULL),
(4,'Delivery Boy','web',NULL,NULL);
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sections`
--

DROP TABLE IF EXISTS `sections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sections` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `row_order` int(11) NOT NULL DEFAULT 0,
  `title` varchar(191) NOT NULL,
  `short_description` varchar(191) NOT NULL,
  `product_type` varchar(191) NOT NULL,
  `product_ids` text DEFAULT NULL,
  `category_ids` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `position` varchar(191) NOT NULL,
  `style_app` varchar(191) NOT NULL,
  `banner_app` varchar(191) DEFAULT NULL,
  `style_web` varchar(191) NOT NULL,
  `banner_web` varchar(191) DEFAULT NULL,
  `background_color_for_light_theme` varchar(191) NOT NULL,
  `background_color_for_dark_theme` varchar(191) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sections`
--

LOCK TABLES `sections` WRITE;
/*!40000 ALTER TABLE `sections` DISABLE KEYS */;
INSERT INTO `sections` VALUES
(1,0,'FLASH SALE','.','custom_products','3,2,1','1,2','2025-06-27 17:04:01','2025-07-27 13:32:06','below_category','style_1','banner_section_style/1751937841_79337.jpg','style_4','banner_section_style/1751954413_68343.jpg','#ffffff','#ff0000');
/*!40000 ALTER TABLE `sections` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seller_commissions`
--

DROP TABLE IF EXISTS `seller_commissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seller_commissions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `seller_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `commission` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seller_commissions`
--

LOCK TABLES `seller_commissions` WRITE;
/*!40000 ALTER TABLE `seller_commissions` DISABLE KEYS */;
INSERT INTO `seller_commissions` VALUES
(1,1,1,0),
(2,1,4,0),
(3,1,3,0),
(4,1,2,0),
(5,1,14,0),
(6,1,12,0),
(7,1,11,0),
(8,1,10,0),
(9,1,9,0),
(10,1,8,0),
(11,1,7,0),
(12,1,5,0);
/*!40000 ALTER TABLE `seller_commissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seller_transactions`
--

DROP TABLE IF EXISTS `seller_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seller_transactions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `seller_id` int(11) DEFAULT NULL,
  `order_id` int(11) DEFAULT NULL,
  `order_item_id` int(11) DEFAULT NULL,
  `type` text DEFAULT NULL,
  `txn_id` text DEFAULT NULL,
  `amount` double NOT NULL DEFAULT 0,
  `status` text DEFAULT NULL,
  `message` text DEFAULT NULL,
  `transaction_date` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seller_transactions`
--

LOCK TABLES `seller_transactions` WRITE;
/*!40000 ALTER TABLE `seller_transactions` DISABLE KEYS */;
/*!40000 ALTER TABLE `seller_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seller_wallet_transactions`
--

DROP TABLE IF EXISTS `seller_wallet_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seller_wallet_transactions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL,
  `order_item_id` int(11) DEFAULT NULL,
  `seller_id` int(11) DEFAULT NULL,
  `type` varchar(191) DEFAULT NULL,
  `amount` double NOT NULL DEFAULT 0,
  `message` text DEFAULT NULL,
  `status` tinyint(4) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seller_wallet_transactions`
--

LOCK TABLES `seller_wallet_transactions` WRITE;
/*!40000 ALTER TABLE `seller_wallet_transactions` DISABLE KEYS */;
INSERT INTO `seller_wallet_transactions` VALUES
(1,NULL,NULL,1,'credit',50,NULL,1,'2025-06-27 17:32:09','2025-06-27 17:32:09'),
(2,NULL,NULL,1,'debit',20,NULL,1,'2025-06-27 17:33:26','2025-06-27 17:33:26');
/*!40000 ALTER TABLE `seller_wallet_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sellers`
--

DROP TABLE IF EXISTS `sellers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sellers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `admin_id` bigint(20) unsigned DEFAULT NULL,
  `name` text DEFAULT NULL,
  `store_name` text DEFAULT NULL,
  `slug` text DEFAULT NULL,
  `email` text DEFAULT NULL,
  `mobile` text DEFAULT NULL,
  `balance` double NOT NULL DEFAULT 0,
  `store_url` text DEFAULT NULL,
  `logo` text DEFAULT NULL,
  `store_description` text DEFAULT NULL,
  `street` text DEFAULT NULL,
  `pincode_id` int(11) DEFAULT NULL,
  `city_id` text DEFAULT NULL,
  `state` text DEFAULT NULL,
  `categories` text DEFAULT NULL,
  `account_number` text DEFAULT NULL,
  `bank_ifsc_code` text DEFAULT NULL,
  `account_name` text DEFAULT NULL,
  `bank_name` text DEFAULT NULL,
  `commission` int(11) DEFAULT 0,
  `status` tinyint(4) NOT NULL,
  `require_products_approval` tinyint(4) NOT NULL DEFAULT 0,
  `fcm_id` text DEFAULT NULL,
  `national_identity_card` text DEFAULT NULL,
  `address_proof` text DEFAULT NULL,
  `pan_number` text DEFAULT NULL,
  `tax_name` text DEFAULT NULL,
  `tax_number` text DEFAULT NULL,
  `customer_privacy` tinyint(4) DEFAULT 0,
  `latitude` varchar(191) DEFAULT NULL,
  `longitude` varchar(191) DEFAULT NULL,
  `place_name` varchar(191) DEFAULT NULL,
  `formatted_address` varchar(191) DEFAULT NULL,
  `forgot_password_code` varchar(191) DEFAULT NULL,
  `view_order_otp` tinyint(4) NOT NULL DEFAULT 0,
  `assign_delivery_boy` tinyint(4) NOT NULL DEFAULT 0,
  `fssai_lic_no` varchar(191) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `remark` text DEFAULT NULL,
  `change_order_status_delivered` text DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sellers`
--

LOCK TABLES `sellers` WRITE;
/*!40000 ALTER TABLE `sellers` DISABLE KEYS */;
INSERT INTO `sellers` VALUES
(1,2,'Zoomfresh','zoomfresh','zoomfresh','<EMAIL>','9995286658',30,'null','sellers/1750430125_99014.png','null','Nilambur  Kerala',0,'1','null','14,12,11,10,9,8,7,5,4,3,2,1','65764','fgskg433','canara','test',10,1,0,NULL,'sellers/1750430125_81010.png','sellers/1750430125_65183.png','clypm9933p','null','null',0,'11.2855356','76.2385793','Nilambur','Nilambur, Kerala, India',NULL,0,0,NULL,'2025-06-20 14:35:25','2025-07-29 18:57:11',NULL,'null','0');
/*!40000 ALTER TABLE `sellers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sessions` (
  `id` varchar(191) NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` text NOT NULL,
  `last_activity` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sessions`
--

LOCK TABLES `sessions` WRITE;
/*!40000 ALTER TABLE `sessions` DISABLE KEYS */;
/*!40000 ALTER TABLE `sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `settings`
--

DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `variable` text NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=171 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `settings`
--

LOCK TABLES `settings` WRITE;
/*!40000 ALTER TABLE `settings` DISABLE KEYS */;
INSERT INTO `settings` VALUES
(1,'0','panel_login_background_img'),
(2,'support_number',' '),
(3,'support_email','<EMAIL>'),
(4,'logo',' '),
(5,'purchase_code','f4ca274a-681b-484e-8f38-409deec105b9'),
(6,'stripe_secret_key','0'),
(7,'stripe_publishable_key','0'),
(8,'stripe_webhook_secret_key','0'),
(9,'1','notification_delay_after_cart_addition'),
(10,'2','notification_interval'),
(11,'3','notification_stop_time'),
(12,'system_configurations','1'),
(13,'system_timezone_gmt','+05:30'),
(14,'system_configurations_id','13'),
(15,'app_name','zoomfresh'),
(16,'is_version_system_on','0'),
(17,'required_force_update','0'),
(18,'current_version','1.0.0'),
(19,'ios_is_version_system_on','0'),
(20,'ios_required_force_update','0'),
(21,'ios_current_version','1.0.0'),
(22,'copyright_details',''),
(23,'store_address','test'),
(24,'map_latitude',' '),
(25,'map_longitude',' '),
(26,'currency','₹'),
(27,'currency_code','INR'),
(28,'decimal_point','0'),
(29,'system_timezone','Asia/Kolkata'),
(30,'default_city_id','1'),
(31,'max_cart_items_count','10'),
(32,'min_order_amount','50'),
(33,'low_stock_limit','1'),
(34,'delivery_boy_bonus_settings','1'),
(35,'delivery_boy_bonus_type','0'),
(36,'delivery_boy_bonus_percentage','0'),
(37,'delivery_boy_bonus_min_amount','0'),
(38,'delivery_boy_bonus_max_amount','0'),
(39,'area_wise_delivery_charge','0'),
(40,'min_amount',' '),
(41,'delivery_charge',' '),
(42,'is_refer_earn_on','0'),
(43,'min_refer_earn_order_amount',' '),
(44,'refer_earn_bonus',' '),
(45,'refer_earn_method',' '),
(46,'max_refer_earn_amount',' '),
(47,'minimum_withdrawal_amount',' '),
(48,'max_product_return_days',' '),
(49,'user_wallet_refill_limit',' '),
(50,'tax_name',' '),
(51,'tax_number',' '),
(52,'from_mail',' '),
(53,'reply_to',' '),
(54,'generate_otp','1'),
(55,'app_mode_customer','0'),
(56,'app_mode_customer_remark',' '),
(57,'app_mode_seller','0'),
(58,'app_mode_seller_remark',' '),
(59,'app_mode_delivery_boy','0'),
(60,'app_mode_delivery_boy_remark',' '),
(61,'smtp_from_mail',' '),
(62,'smtp_reply_to',' '),
(63,'smtp_email_password',' '),
(64,'smtp_host',' '),
(65,'smtp_port',' '),
(66,'smtp_content_type',' '),
(67,'smtp_encryption_type',' '),
(68,'google_place_api_key','U2FsdGVkX19fCawJkz4xYiXVxXhCZRNLsjUy4AD5ZnCX440qY/0UoUf4jj+ozprCVwIdQEEhKqMOFq0Xvb9Ibg=='),
(69,'apiKey','AIzaSyBzPHaMl9fEWZN2HTS107imbC_gguQTI8M'),
(70,'fssai_lic_img',' '),
(71,'is_category_section_in_homepage','1'),
(72,'is_brand_section_in_homepage','0'),
(73,'is_seller_section_in_homepage','0'),
(74,'is_country_section_in_homepage','0'),
(75,'count_category_section_in_homepage','12'),
(76,'count_brand_section_in_homepage','6'),
(77,'count_seller_section_in_homepage','5'),
(78,'count_country_section_in_homepage','5'),
(79,'one_seller_cart',' '),
(80,'playstore_url',' '),
(81,'appstore_url',' '),
(82,'guest_cart',' '),
(83,'phone_login','1'),
(84,'google_login',' '),
(85,'apple_login',' '),
(86,'email_login',' '),
(87,'authDomain','zoomfresh-6c28a.firebaseapp.com'),
(88,'databaseURL',' '),
(89,'projectId','zoomfresh-6c28a'),
(90,'storageBucket','zoomfresh-6c28a.appspot.com'),
(91,'messagingSenderId','995833245745'),
(92,'appId','1:995833245745:android:d6e0e0bc5f1e9aa52e2510'),
(93,'measurementId','G-HQ2D7TDB80'),
(94,'jsonFile','{\"project_info\":{\"project_number\":\"995833245745\",\"project_id\":\"zoomfresh-6c28a\",\"storage_bucket\":\"zoomfresh-6c28a.appspot.com\"},\"client\":[{\"client_info\":{\"mobilesdk_app_id\":\"1:995833245745:android:1762c7a4fcb3efa32e2510\",\"android_client_info\":{\"package_name\":\"com.wrteam.egrocer\"}},\"oauth_client\":[],\"api_key\":[{\"current_key\":\"AIzaSyBdMWSyF66ipCrj1_qJIN0GcPlvEKZ2Gno\"}],\"services\":{\"appinvite_service\":{\"other_platform_oauth_client\":[]}}}],\"configuration_version\":\"1\"}'),
(95,'phone_auth_otp','1'),
(96,'phone_auth_password','0'),
(97,'firebase_authentication','1'),
(98,'custom_sms_gateway_otp_based','0'),
(99,'payment_method_settings','0'),
(100,'cod_payment_method','1'),
(101,'cod_mode','global'),
(102,'paypal_payment_method','undefined'),
(103,'paypal_mode','0'),
(104,'paypal_currency_code','0'),
(105,'paypal_business_email','undefined'),
(106,'paypal_notification_url','https://zoomfresh.co.in/customer/ipn'),
(107,'payumoney_payment_method','undefined'),
(108,'payumoney_mode','0'),
(109,'payumoney_merchant_key','undefined'),
(110,'payumoney_merchant_id','undefined'),
(111,'payumoney_salt','undefined'),
(112,'razorpay_payment_method','1'),
(113,'razorpay_key','***********************'),
(114,'razorpay_secret_key','Gnote8AHlK35KTT6xtHFrfNC'),
(115,'paystack_payment_method','undefined'),
(116,'paystack_public_key','undefined'),
(117,'paystack_secret_key','undefined'),
(118,'paystack_currency_code','0'),
(119,'flutterwave_payment_method','undefined'),
(120,'flutterwave_public_key','undefined'),
(121,'flutterwave_secret_key','undefined'),
(122,'flutterwave_encryption_key','undefined'),
(123,'flutterwave_currency_code','0'),
(124,'stripe_payment_method','undefined'),
(125,'stripe_webhook_url','https://zoomfresh.co.in/webhook/stripe'),
(126,'stripe_currency_code','undefined'),
(127,'stripe_mode','undefined'),
(128,'paytm_payment_method','undefined'),
(129,'paytm_mode','0'),
(130,'paytm_merchant_key','undefined'),
(131,'paytm_merchant_id','undefined'),
(132,'ssl_commerce_payment_method','undefined'),
(133,'ssl_commerece_mode','0'),
(134,'ssl_commerece_store_id','undefined'),
(135,'ssl_commerece_secret_key','undefined'),
(136,'direct_bank_transfer','undefined'),
(137,'account_name','undefined'),
(138,'account_number','undefined'),
(139,'bank_name','undefined'),
(140,'bank_code','undefined'),
(141,'notes','undefined'),
(142,'midtrans_payment_method','undefined'),
(143,'midtrans_mode','undefined'),
(144,'midtrans_server_key','undefined'),
(145,'midtrans_notification_url','https://zoomfresh.co.in/midtrans/callback'),
(146,'midtrans_return_url','/web-payment-status'),
(147,'phonepay_payment_method','undefined'),
(148,'phonepay_mode','undefined'),
(149,'phonepay_api_key','undefined'),
(150,'phonepay_merchant_id','undefined'),
(151,'cashfree_payment_method','undefined'),
(152,'cashfree_mode','undefined'),
(153,'cashfree_app_id','undefined'),
(154,'cashfree_secret_key','undefined'),
(155,'cashfree_notification_url','https://zoomfresh.co.in/cashfree/callback'),
(156,'paytabs_payment_method','undefined'),
(157,'paytabs_mode','undefined'),
(158,'paytabs_profile_id','undefined'),
(159,'paytabs_secret_key','undefined'),
(160,'paytabs_notification_url','https://zoomfresh.co.in/paytabs/callback'),
(161,'time_slots_is_enabled','true'),
(162,'time_slots_allowed_days','2'),
(163,'delivery_estimate_days','1'),
(164,'cart_notification','1'),
(165,'popup_enabled','1'),
(166,'popup_always_show_home','0'),
(167,'popup_type','product'),
(168,'popup_type_id','4'),
(169,'popup_image','offers/1752420471_popup_76589.png'),
(170,'popup_url','undefined');
/*!40000 ALTER TABLE `settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sliders`
--

DROP TABLE IF EXISTS `sliders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sliders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(191) NOT NULL,
  `type_id` varchar(191) NOT NULL,
  `image` varchar(191) NOT NULL,
  `slider_url` text DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1-active, 0-deactive',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sliders`
--

LOCK TABLES `sliders` WRITE;
/*!40000 ALTER TABLE `sliders` DISABLE KEYS */;
INSERT INTO `sliders` VALUES
(5,'product','2','sliders/1751197490_66481.jpg',NULL,1,'2025-06-29 17:14:50','2025-06-29 17:14:50'),
(7,'category','3','sliders/1753543042_62818.jpg','null',1,'2025-07-26 20:47:22','2025-07-26 20:49:41'),
(8,'category','2','sliders/1753543068_81415.jpg',NULL,1,'2025-07-26 20:47:48','2025-07-26 20:47:48'),
(9,'category','2','sliders/1753543296_38910.jpg',NULL,1,'2025-07-26 20:51:36','2025-07-26 20:51:36'),
(10,'default','0','sliders/1753600623_22948.jpg',NULL,1,'2025-07-27 12:47:03','2025-07-27 12:47:03');
/*!40000 ALTER TABLE `sliders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sms_templates`
--

DROP TABLE IF EXISTS `sms_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sms_templates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sms_templates`
--

LOCK TABLES `sms_templates` WRITE;
/*!40000 ALTER TABLE `sms_templates` DISABLE KEYS */;
INSERT INTO `sms_templates` VALUES
(1,'Your Order Has Been Placed - Order #{order.id}','Dear [Customer Name], your order #[Order ID] has been successfully placed. You will receive updates as your order is processed. Thank you for shopping with us!','customer_place_order','2025-06-20 14:17:32',NULL),
(2,'Payment Pending for Order #{order.id}','Dear [Customer Name], your order [Order ID] is awaiting payment. Please complete the payment to process your order. If you need assistance, contact [Support Contact].','customer_order_payment_pending','2025-06-20 14:17:32',NULL),
(3,'Your Order Has Been Received - Order #{order.id}','Dear [Customer Name], we have received your order [Order ID] and it\'s now being processed. You will receive updates on shipping soon. Thank you for choosing [Store Name]!','customer_order_received','2025-06-20 14:17:32',NULL),
(4,'Your Order Has Been Processed - Order #{order.id}','Dear [Customer Name], your order [Order ID] has been processed and is ready for shipment. You will be notified once it\'s on the way. Thank you for shopping with us!','customer_order_processed','2025-06-20 14:17:32',NULL),
(5,'Your Order Has Been Shipped - Order #{order.id}','Dear [Customer Name], your order [Order ID] has been shipped. Thank you for choosing [Store Name]!','customer_order_shipped','2025-06-20 14:17:32',NULL),
(6,'Your Order is Out for Delivery - Order #{order.id}','Dear [Customer Name], your order [Order ID] is out for delivery and will reach you soon. Please be available to receive your package. Thank you!','customer_order_out_for_delivery','2025-06-20 14:17:32',NULL),
(7,'Your Order Has Been Delivered - Order #{order.id}','Dear [Customer Name], your order [Order ID] has been successfully delivered. We hope you enjoy your purchase. Thank you for shopping at [Store Name]!','customer_order_delivered','2025-06-20 14:17:32',NULL),
(8,'Your Order Has Been Cancelled - Order #{order.id}','Dear [Customer Name], your order Item [Product Name] from Order [Order ID] has been cancelled. If you have any questions or wish to place a new order, please contact [Support Contact].','customer_order_cancelled','2025-06-20 14:17:32',NULL),
(9,'Return Request Received for Order #{order.id}','Dear [Customer Name], we have received your return request for order Item [Product Name] from Order [Order ID]. Our team will review it and get back to you shortly with the next steps.','customer_order_return_request','2025-06-20 14:17:32',NULL),
(10,'Return Request Approved - Order #{order.id}','Dear [Customer Name], your return request for order Item [Product Name] from Order [Order ID] has been approved. The refund amount will be credited to your wallet. Thank you!','customer_order_confirm_return_request','2025-06-20 14:17:32',NULL),
(11,'Return Request Rejected - Order #{order.id}','Dear [Customer Name], your return request for order Item [Product Name] from Order [Order ID] has been rejected. Reason: [Reason]. If you have any questions, please contact our support team. Thank you for understanding.','customer_order_reject_return_request','2025-06-20 14:17:32',NULL);
/*!40000 ALTER TABLE `sms_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sms_verifications`
--

DROP TABLE IF EXISTS `sms_verifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sms_verifications` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `phone` varchar(191) NOT NULL,
  `otp` varchar(191) NOT NULL,
  `status` varchar(191) NOT NULL DEFAULT 'pending',
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sms_verifications`
--

LOCK TABLES `sms_verifications` WRITE;
/*!40000 ALTER TABLE `sms_verifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `sms_verifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `social_media`
--

DROP TABLE IF EXISTS `social_media`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `social_media` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `icon` text NOT NULL,
  `link` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `social_media`
--

LOCK TABLES `social_media` WRITE;
/*!40000 ALTER TABLE `social_media` DISABLE KEYS */;
/*!40000 ALTER TABLE `social_media` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sub_categories`
--

DROP TABLE IF EXISTS `sub_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sub_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `row_order` int(11) NOT NULL DEFAULT 0,
  `category_id` int(11) NOT NULL,
  `name` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `subtitle` text NOT NULL,
  `image` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sub_categories`
--

LOCK TABLES `sub_categories` WRITE;
/*!40000 ALTER TABLE `sub_categories` DISABLE KEYS */;
/*!40000 ALTER TABLE `sub_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `subscription`
--

DROP TABLE IF EXISTS `subscription`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscription` (
  `subscription_id` int(11) NOT NULL AUTO_INCREMENT,
  `subscription_name` varchar(255) DEFAULT NULL,
  `subscription_price` decimal(11,2) DEFAULT NULL,
  `subscription_discount_price` decimal(11,2) DEFAULT NULL,
  `subscription_description` varchar(500) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`subscription_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `subscription`
--

LOCK TABLES `subscription` WRITE;
/*!40000 ALTER TABLE `subscription` DISABLE KEYS */;
INSERT INTO `subscription` VALUES
(1,'1 month VIP Plan',9.00,NULL,'Free delivery on all orders',1,'2025-07-10 16:19:59','2025-07-10 16:21:34'),
(2,'3 month VIP Plan',119.00,19.00,'Free delivery on all orders',1,'2025-07-10 16:19:59','2025-07-10 16:19:59'),
(3,'6 month VIP Plan',299.00,49.00,'Free delivery on all orders',1,'2025-07-10 16:19:59','2025-07-10 16:19:59');
/*!40000 ALTER TABLE `subscription` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `subscription_items`
--

DROP TABLE IF EXISTS `subscription_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscription_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `subscription_id` bigint(20) unsigned NOT NULL,
  `stripe_id` varchar(191) NOT NULL,
  `stripe_product` varchar(191) NOT NULL,
  `stripe_price` varchar(191) NOT NULL,
  `quantity` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subscription_items_subscription_id_stripe_price_unique` (`subscription_id`,`stripe_price`),
  UNIQUE KEY `subscription_items_stripe_id_unique` (`stripe_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `subscription_items`
--

LOCK TABLES `subscription_items` WRITE;
/*!40000 ALTER TABLE `subscription_items` DISABLE KEYS */;
/*!40000 ALTER TABLE `subscription_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `subscriptions`
--

DROP TABLE IF EXISTS `subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `subscriptions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `name` varchar(191) NOT NULL,
  `stripe_id` varchar(191) NOT NULL,
  `stripe_status` varchar(191) NOT NULL,
  `stripe_price` varchar(191) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `ends_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subscriptions_stripe_id_unique` (`stripe_id`),
  KEY `subscriptions_user_id_stripe_status_index` (`user_id`,`stripe_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `subscriptions`
--

LOCK TABLES `subscriptions` WRITE;
/*!40000 ALTER TABLE `subscriptions` DISABLE KEYS */;
/*!40000 ALTER TABLE `subscriptions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `supported_languages`
--

DROP TABLE IF EXISTS `supported_languages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `supported_languages` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `code` varchar(191) NOT NULL,
  `type` varchar(191) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `supported_languages`
--

LOCK TABLES `supported_languages` WRITE;
/*!40000 ALTER TABLE `supported_languages` DISABLE KEYS */;
INSERT INTO `supported_languages` VALUES
(1,'Afrikaans','af','ltr'),
(2,'Amharic','am','ltr'),
(3,'Arabic','ar','rtl'),
(4,'Assamese','as','ltr'),
(5,'Azerbaijani','az','ltr'),
(6,'Belarusian','be','ltr'),
(7,'Bulgarian','bg','ltr'),
(8,'Bengali (Bangla)','bn','ltr'),
(9,'Bosnian','bs','ltr'),
(10,'Catalan (Valencian)','ca','ltr'),
(11,'Czech','cs','ltr'),
(12,'Welsh','cy','ltr'),
(13,'Danish','da','ltr'),
(14,'German','de','ltr'),
(15,'Modern Greek','el','ltr'),
(16,'English','en','ltr'),
(17,'Spanish (Castilian)','es','ltr'),
(18,'Estonian','et','ltr'),
(19,'Basque','eu','ltr'),
(20,'Persian','fa','rtl'),
(21,'Finnish','fi','ltr'),
(22,'Filipino (Pilipino)','fil','ltr'),
(23,'French','fr','ltr'),
(24,'Galician','gl','ltr'),
(25,'Swiss German (Alemannic, Alsatian)','gsw','ltr'),
(26,'Gujarati','gu','ltr'),
(27,'Hebrew','he','rtl'),
(28,'Hindi','hi','ltr'),
(29,'Croatian','hr','ltr'),
(30,'Hungarian','hu','ltr'),
(31,'Armenian','hy','ltr'),
(32,'Indonesian','id','ltr'),
(33,'Icelandic','is','ltr'),
(34,'Italian','it','ltr'),
(35,'Japanese','ja','ltr'),
(36,'Georgian','ka','ltr'),
(37,'Kazakh','kk','ltr'),
(38,'Khmer (Central Khmer)','km','ltr'),
(39,'Kannada','kn','ltr'),
(40,'Korean','ko','ltr'),
(41,'Kirghiz (Kyrgyz)','ky','ltr'),
(42,'Lao','lo','ltr'),
(43,'Lithuanian','lt','ltr'),
(44,'Latvian','lv','ltr'),
(45,'Macedonian','mk','ltr'),
(46,'Malayalam','ml','ltr'),
(47,'Mongolian','mn','ltr'),
(48,'Marathi','mr','ltr'),
(49,'Malay','ms','ltr'),
(50,'Burmese','my','ltr'),
(51,'Norwegian Bokmål','nb','ltr'),
(52,'Nepali','ne','ltr'),
(53,'Dutch (Flemish)','nl','ltr'),
(54,'Norwegian','no','ltr'),
(55,'Oriya','or','ltr'),
(56,'Panjabi (Punjabi)','pa','ltr'),
(57,'Polish','pl','ltr'),
(58,'Pushto (Pashto)','ps','rtl'),
(59,'Portuguese','pt','ltr'),
(60,'Romanian (Moldavian, Moldovan)','ro','ltr'),
(61,'Russian','ru','ltr'),
(62,'Sinhala (Sinhalese)','si','ltr'),
(63,'Slovak','sk','ltr'),
(64,'Slovenian','sl','ltr'),
(65,'Albanian','sq','ltr'),
(66,'Serbian','sr','ltr'),
(67,'Swedish','sv','ltr'),
(68,'Swahili','sw','ltr'),
(69,'Tamil','ta','ltr'),
(70,'Telugu','te','ltr'),
(71,'Thai','th','ltr'),
(72,'Tagalog','tl','ltr'),
(73,'Turkish','tr','ltr'),
(74,'Ukrainian','uk','ltr'),
(75,'Urdu','ur','rtl'),
(76,'Uzbek','uz','ltr'),
(77,'Vietnamese','vi','ltr'),
(78,'Chinese','zh','ltr'),
(79,'Zulu','zu','ltr');
/*!40000 ALTER TABLE `supported_languages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tags`
--

DROP TABLE IF EXISTS `tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tags` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tags_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tags`
--

LOCK TABLES `tags` WRITE;
/*!40000 ALTER TABLE `tags` DISABLE KEYS */;
/*!40000 ALTER TABLE `tags` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `taxes`
--

DROP TABLE IF EXISTS `taxes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `taxes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` text DEFAULT NULL,
  `percentage` double NOT NULL DEFAULT 0,
  `status` tinyint(4) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `taxes`
--

LOCK TABLES `taxes` WRITE;
/*!40000 ALTER TABLE `taxes` DISABLE KEYS */;
/*!40000 ALTER TABLE `taxes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `time_slots`
--

DROP TABLE IF EXISTS `time_slots`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `time_slots` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(191) NOT NULL,
  `from_time` time NOT NULL,
  `to_time` time NOT NULL,
  `last_order_time` time NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1-active, 0-deactive',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `time_slots`
--

LOCK TABLES `time_slots` WRITE;
/*!40000 ALTER TABLE `time_slots` DISABLE KEYS */;
INSERT INTO `time_slots` VALUES
(1,'Morning','08:00:00','20:00:00','19:00:00',1,'2025-06-26 14:33:29','2025-06-26 14:33:29'),
(2,'Evening','14:00:00','13:00:00','19:35:00',1,'2025-06-26 14:35:14','2025-06-26 14:35:33');
/*!40000 ALTER TABLE `time_slots` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transactions`
--

DROP TABLE IF EXISTS `transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transactions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `order_id` varchar(191) NOT NULL,
  `type` varchar(191) NOT NULL,
  `txn_id` varchar(191) NOT NULL,
  `payu_txn_id` varchar(191) DEFAULT NULL,
  `amount` double NOT NULL,
  `status` varchar(191) NOT NULL,
  `message` varchar(191) NOT NULL,
  `transaction_date` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transactions_user_id_index` (`user_id`),
  KEY `transactions_order_id_index` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transactions`
--

LOCK TABLES `transactions` WRITE;
/*!40000 ALTER TABLE `transactions` DISABLE KEYS */;
INSERT INTO `transactions` VALUES
(1,1,'8','Razorpay','pay_QmFWzFNrWR5yEv','',10,'success','','2025-06-27 19:45:02','2025-06-27 19:45:02','2025-06-27 19:45:02');
/*!40000 ALTER TABLE `transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `units`
--

DROP TABLE IF EXISTS `units`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `units` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `short_code` varchar(191) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `conversion` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `units`
--

LOCK TABLES `units` WRITE;
/*!40000 ALTER TABLE `units` DISABLE KEYS */;
INSERT INTO `units` VALUES
(1,'kilogram','kg',0,1),
(2,'Cutting style','C S',0,1),
(3,'Cleaned','Cleaned',2,1),
(4,'Chopped','Chopped',2,1),
(6,'. 500grm','Gram',0,1),
(7,'Gram','Gm',6,1),
(8,'Gram','gm',0,1);
/*!40000 ALTER TABLE `units` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `updates`
--

DROP TABLE IF EXISTS `updates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `updates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `version` varchar(191) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `updates`
--

LOCK TABLES `updates` WRITE;
/*!40000 ALTER TABLE `updates` DISABLE KEYS */;
/*!40000 ALTER TABLE `updates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_addresses`
--

DROP TABLE IF EXISTS `user_addresses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_addresses` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` varchar(191) NOT NULL,
  `name` varchar(191) NOT NULL,
  `mobile` varchar(191) NOT NULL,
  `alternate_mobile` varchar(191) DEFAULT NULL,
  `address` text NOT NULL,
  `landmark` text NOT NULL,
  `area` varchar(191) NOT NULL,
  `pincode` varchar(191) NOT NULL,
  `city_id` varchar(191) NOT NULL,
  `city` varchar(191) NOT NULL,
  `state` varchar(191) NOT NULL,
  `country` varchar(191) NOT NULL,
  `is_default` tinyint(4) NOT NULL DEFAULT 0,
  `latitude` varchar(191) DEFAULT NULL,
  `longitude` varchar(191) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_addresses`
--

LOCK TABLES `user_addresses` WRITE;
/*!40000 ALTER TABLE `user_addresses` DISABLE KEYS */;
INSERT INTO `user_addresses` VALUES
(1,1,'office','hurair','9544794516','7561079073','76PR+G8C, Chandakunnu, Nilambur, Kerala 679330, India','fairy land','chandakunnu','679329','1','Nilambur','Kerala','India',1,'11.2863436','76.2406147','2025-06-26 14:00:38','2025-06-26 14:00:38'),
(2,2,'home','maaz','9995286658','9745506727','12, Chandakunnu, Nilambur, Kerala 679329, India','near smile smart dental care','chandakunnu','682030','1','Nilambur','Kerala','India',1,'11.2855356','76.2385793','2025-06-26 19:45:05','2025-06-26 19:46:00'),
(3,4,'home','Sameer Vellamunda','9645805427','6235123962','77V9+FP3, Nilambur, Kerala 679330, India','ST Thomas Public English School','Enanthi','679330','0','Nilambur','Kerala','India',1,'11.2936376','76.2692916','2025-07-04 13:55:27','2025-07-04 13:55:27'),
(4,6,'home','Rupesh R Menon','7306276083','9496320663','X82Q+6GP, Thamaramkulangara Rd, Vadakkekotta, Kottakakom, Thrippunithura, Ernakulam, Kochi, Kerala 682301, India','Vadakkekotta','Thamaramkulangara Road','682301','0','Ernakulam','Kerala','India',1,'9.9505733','76.3388323','2025-07-09 10:06:00','2025-07-09 10:06:00'),
(5,3,'home','testuser','9495037815',NULL,'12, Chandakunnu, Nilambur, Kerala 679329, India','testadress','test','543210','1','Nilambur','Kerala','India',1,'11.2855356','76.2385793','2025-07-11 23:18:48','2025-07-11 23:20:09'),
(6,13,'home','Burhan','7012836907','1234567890','12, Chandakunnu, Nilambur, Kerala 679329, India','landmark','area','679329','1','Nilambur','Kerala','India',1,'11.2855356','76.2385793','2025-07-25 13:53:21','2025-07-25 13:53:55');
/*!40000 ALTER TABLE `user_addresses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_tokens`
--

DROP TABLE IF EXISTS `user_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` varchar(191) NOT NULL,
  `fcm_token` varchar(255) NOT NULL,
  `platform` varchar(191) NOT NULL DEFAULT 'web',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_tokens`
--

LOCK TABLES `user_tokens` WRITE;
/*!40000 ALTER TABLE `user_tokens` DISABLE KEYS */;
INSERT INTO `user_tokens` VALUES
(1,0,'customer','ddy4E9I7RMGhXBf53034cf:APA91bHVWK2FWTXK1br9oHDPeglQmr_iBLL1ZiMjsDcQ_neRwXMh-Ypoh4c0HHcJCB1MPe8lS5yiS6Od237vqs4dLz99DzKxK0j99JPB0cAKXUpQHhBsGZc','android'),
(2,0,'customer','cfDenLkxQ6mQEm18OI-nl8:APA91bG9QGgDRmnDRpNahiznSet-ULNjqTNRs40p_Usf2_f4u-lo6FWnIhFIhwCM7aJMQFseqKw4ipJKGQrlCvCU8lI7BxtaT5nnX2kM8ouDibcZdOe6DWo','android'),
(3,0,'customer','cinjzERkSfOOfIGotLaI0d:APA91bE3f1yWgC1MqDpVMjOuBzCIUDEDYYO-MyFZ5HXsc-xxAl27Vl558QD1-8PfNyUqQRvMs6WqGfV8IHOOUEUg0GsFqtBjhqNyVe6oFR2VHVUnAFG73no','android'),
(4,0,'customer','fivwSYGlSmWectLlykLBrH:APA91bGZQCpGF6P55wvKRguLg-TGbgSiEPGwCg1Sdso4rM771veQZmbfzGd4gtS5Cme1RPyBsqgkS4nFqFtiKbQJl7rLcpbGm6IIPe5eMjNs5d6WGcccV-A','android'),
(5,0,'customer','dXUUFK-cSu-CMEgTlRgOrY:APA91bHnp4kC-cFqCeERmyf1bQN-g4f7cN_MOi0xyvYl5N70VNzJps_016fqH-1KJowM1Buh12u1z3fFTUCePGJR6PgnCZmRy83fu1LXiniFwqkYWbi5CRg','android'),
(6,0,'customer','dA9tNoE_QLi28wsAs9szrR:APA91bHGfxk59-AUkivYr0oeQyMNaPknmszha_mmjAk1RhQg5Wotvdy7nN3M1mGjjKtWzPM6pKL0E5qsVL9susk6Cs17X9mzwwmt3ZjYanbZu8Vzy0d6h5o','android'),
(7,1,'customer','dq2hODT5TtyVa8ay27d9sK:APA91bFfrIhmVXjjoN6nJkg4QwcNX1AONzWwa1X2vT3q0SQ5VcPOp49de4a3l2x26Vj920cUf8L4ftqyEr-G0_nVgU3pOpyS5KfyBkjEr7BtylJs3959lMQ','android'),
(8,0,'customer','fMmmnXHfSTeYu_LTTcuqtT:APA91bH6zKm3w5A3wa1eGh8zv__y3I5J7J9hL3XdsTOqu763I6SL6LYchiiqHYQcmjBbW07orvaqfwPtY-M3Wqa_7gb7ejVjD_cgUOw8UKzHAgNAW9SdSM0','android'),
(9,2,'customer','fprkfa4aQ3aU2m8HzrcLtd:APA91bEAaF-g-pz0_DLJT93AkqB5Xu_8PglHV0C0M9kh9vLTEdg8N_TCfw2yej1KQ1WpnHaZlPLefV_7f3hJw-iV-SayAVp2OkrOTQSE2xqwWX85C-XmLRo','android'),
(10,1,'customer','f46bsY3LTCy7_72f1PAIbP:APA91bGK5tBqY3UX3f5jXx5NaWWevPuhrcExNIpVL4NQGuBePD6DkGBQoj6gJ6A01sU7hucbqnRRAWs4dxFcV2zGlzYS-QdtTn9RfJOEjF1w4GfEROAtzE4','android'),
(11,3,'customer','dytqWoeoRaW98JsO0w5SVT:APA91bFPub_4kppakeT1BOCPuyVwnfsXJgLz-wnrLcMcAg3wr5zrTv_5jRE3GUUKpJzF58dOKLwVRAWjr9ORCStGQLH9caAxgTQspfuA6jUo1Dqr_n5Twu0','android'),
(12,0,'customer','fykfTwJcRWqe8oL-gPNHSy:APA91bELUMygtxHDRRDAeqQCp0KCsvqz2OVmZT2IicdM0KNRpWx0BTlJO-ZnR0tJ1TtJk8ObaCiz4trE9uS1RY12pxCeUsDYnxaywHQVted2svggHGdpyp4','android'),
(13,0,'customer','cRNebFwOSIm2eSVb-OYzsj:APA91bEuabD4nJ1MzdSfOHqbE0Welha8luFdOexXa_GBIVUzippGB45RWcqMsb1C0IeeQf91-iERyev_w8wOb5giNq2oOdIVpdUZ5UVSGdBqHQPVqT0N33k','android'),
(14,3,'customer','fPxyiHIQSGinA94YgqId3i:APA91bG1zGYFaCNlsQjGfR1ddu3h2rlRKjvjARJxos49NAf_cQHzSlWaoZn7_UlhwH_nfTCV1jcMwP0xrLAm-dY0wfdFlN29DGaFbV6NPi32lyuE2hOZ1TI','android'),
(15,0,'customer','eQA5ijVuQ9i9ZKyM1ggLcr:APA91bGQkja-zottGDxUEAob6MRUlxjcMEmFadCLiEzJO4fMxypits596FylnSL_EyHKPeDWLmsCSGb1OogUtq4SneiLgMP9zvlcAGwIPEWcU-MIE65IA88','android'),
(16,0,'customer','ePwF8fRWR_WXeM01xHABLt:APA91bEAh2qVGFaK-P6JIXhlZyCI9Nxz_Hl5HHBbMdj2umIhJXHxuxtE1lRGAqJNPRw1Qx3kZjKoxz9fcIxj_KhXbFs_BDHoTbSUDLFZ8RJlzF2jthr_uBI','android'),
(17,1,'customer','e3MltIJRTK2vm8TB0aASdp:APA91bGgFjQZV7ZrMg-TIQJu8sB8QI0LmP-akuvpvr676bDzpp71JMhAgBlqy3qjXB3p-KwrhZ3Z7Ea38FM4udHDkwEq9DeoZQqvD5zgSK819bmPrUnCZRY','android'),
(18,4,'customer','fZKjpsQAQ0agBZjOOFGOct:APA91bFL5nFnEv-hXUrJShCkBpbr6TrTMy72U3zqqzm5JQYWHpodV_M311uXJIlcsNYWXNv5cLJ62VOCd_QULPNOXoenxir7EYXmsfHqACg_kElPBPy1U-c','android'),
(19,0,'customer','dj7DIqvbRciex4n128LGG5:APA91bHVLET-FKRbYMPoEXl8Q-r3_Z5zNhLmvNklO888Byv_r_BNu8YSyt1bgBe-49fACIocBadlE7HrpttWtIukfuvmNUOik1M4rurlDtKfRwS2faKcJa4','android'),
(20,3,'customer','cP65yVvZQnm4uXz0gOZVO3:APA91bFG22fXF_lW918L1-7BXLvJaf7NpZ74YgUpuSXdN_limqEM0hu_zhbbmbLrMP5GKVIHFfNG4c5JnL88PGWhYPi3GUHpH0fqqkrp44znOQLTUbMWwGo','android'),
(21,2,'customer','fF8wLuxVTTetqHA61Hhunh:APA91bESgva_w8OXI_cazi1i2YMqOeMbbdR2bvLBcr7lf-6BLAoKFsYBhG1T_S9nDDVKU3yF4-tFB2uVaT1HbOM2WhFV3bghj_5mmc_IsKDEurz3G2Pu9s4','android'),
(22,1,'customer','cLqiBlwHTTOPJ-YZm5DatM:APA91bGy9VbLw6CQ5N7w_y6AnfFlxhh6AtsxHw4LqVyInpIg_-YO2hlAemBPdccve8f-YspeB23JKLK-mS2z9mwDyI8YO2FTeG1k7mNbeewWqupQGcP3660','android'),
(23,0,'customer','f6pZ-bX6Qx-gYVcp1CbXP8:APA91bHEmIF8AeFr-D3XmKrhF4CiiJm3cypRyHhbW-pl3cFaIEIDDigTcD1rrgxqt4N47fi1Cs7JjakAMun49YAci597W_y1BEr91-vy_tGKeEfL7eiflH0','android'),
(24,5,'customer','d4qsPXmaRD6On-jJDbXEfm:APA91bGhy6tgNWP0YGP7JM_OXHlahPomw6lTOwOBZtIezqIdfw0pyq2hQ6j_8wFk0dbVw7m-SWB9b0Rmp7ybgKgiAZNlz9rVuGUdJjFhRAmvuZ5Bvgx2_J0','android'),
(25,0,'customer','cEOgqPsDTCC0dnvGviQgo6:APA91bFgSACUmuZfmOAg1xc3DVfxXaoidZUCG8yFOYBvP3FQRjEAGh5YenYAAtg7IbBrw-G_Ixg-XKEY3FKSg8rbgwd5_VZx6g6EY4Avqdmk6oIjQqafANM','android'),
(26,0,'customer','dQSj4KWtQqqpwklEhjm2xu:APA91bER6JBqodV0ADrQ2sSD8Lg_PRJHkOumqzH5U14muIqKZdDobVIvNL7LsYKr6ITCpmwj9C3bxHVdezUtFrA8x7B5Ijgic8cQoHHuwM0wJ1QFVhNuq5Q','android'),
(27,0,'customer','e0x_fxLcSZaZyvvgQ6lxwu:APA91bG5wVirEzFktTpfOMwCZLT75vqn_blBUPkMwFRUbAfjb_n-AeFPb4sM72uQbX0CtEE-6MEJmxiaPpIv5xrhHJmtYFXN5y_sQRy7IyxBdj3taecnRHA','android'),
(28,0,'customer','c1CdIeEnQFSar3m0zjmlSS:APA91bH_YzxmREsP95yvoF-OnsoGjPXhQA2XQOb9-I2iCg1e9X8L7c8soZO74xRkDuK71v-MYdBEfJNWzTg0qfxL_Htp41WjYxqv_bqsAbzlVeMwQD2ccJU','android'),
(29,0,'customer','c0atZxiISqCRLVeDp11pDO:APA91bGIxNJfLakb5IVgOnJLjWz0oHYdXsBdI5HPpHkUsM9CVAtQWtlk-EEHCg9HyQEtRH6XgxJl777p1eQIVIQHCfie59f0XV4zsnXPJhZX5sTBuG3Rq5s','android'),
(30,0,'customer','cWzAZQakS6SFKY_tPJCipe:APA91bH13aRyk_Y6BMVj-8RGYhl_Cw6WvQYcBXfL5qHTVcd7h_ckLV_ChncsTZXUKVofd4-LzfJ-2JdytkFmj3psA37Np-L4Oofh0vGROI02zI0YOD0t3Io','android'),
(31,0,'customer','eJnCjJVZQR6_av4727QEKv:APA91bGcv9ZPG6jDgNF1yLzmuS2zt8KW5mfCrzyoIO8xWrUDwi_PZbQoQLwoJb3QOl3V99c7LNAgzTfz6HxYxSdA0Qz1C-syb0nHYe4nb1Mfbj2RE_D--T4','android'),
(32,0,'customer','c-TMSpwUTaeIDYl8GpmhjA:APA91bEEt30IN2zq-Yjrie4lhRClYKsIdBco5eiantMSFDdecskifwPOMCv8T3cfv7zpnUXUV5SAbv8AvXiKO9MlZQnETM92XYv280BCaJvzYQzd3z5LCIs','android'),
(33,0,'customer','dNAqQBBfQ5202vBWDRWbf8:APA91bEbDt1C1ImS8H5rxXnBSlQFE2rwoYuQNpzlAn6MH98caaJrqwo3uOI58jYswGAoBVWRxzgd4ZaE1ObeXi9Lt89lJkGl9jq6EknafB4gb_ZXvwZwD8E','android'),
(34,0,'customer','foTLtFYPRuyvoorUfgUDiq:APA91bG6hDXvgqwZDoSTa1TS9tS9Ez4Rmigi-qYnh8f-sOy14dmO0RaAogAMSBsL2H4HMXxY4_mqB1dCXPfFhGUZePXQAQAJrLiYtOFRMf6zfpnKJ0dchUM','android'),
(35,0,'customer','fKllCDhqTAycNZ0IBj6BEf:APA91bGlTykxSugyETpP-sCqh3R6akwyahk6Ur_ABWHYefbWVut6s_fjQkbtWbCx4qVfcDt6F3-AXjPHaZq0yMBTO0vF5HgyYiE-BqqNrgRa-xzYzHAR4f0','android'),
(36,0,'customer','dSqOuOZIRQORZJChZPucM3:APA91bEnuDaBYCbO23-O9wXv9itvGFiAFESl_N_gcDRZLKh6EL7ighisje4Gx-KIrs9Mzo6QMtsQHeVC1X07FWktSn5NzUUzYtiWKBKxU2H_3GP_xZiAioQ','android'),
(37,0,'customer','dXUvNT10TEOLU9cO__Avqi:APA91bFgDC8dmONSkN7MGHrFBWPcylVAZ5WUW4-HFlHsrJZdnp7GmscbihjQ6PixMnzc5d2pikQiMP_BEgJDPqGeO-O_P5dlBY7UDNXM7m7Tov-5u9nrJ-U','android'),
(38,0,'customer','c5bK2sW_T3itjNWGadOywG:APA91bFxsM_DiC17Xh3qPVespVsM_JtkLnDlTqcs2TR0YVFwQJUxZht7u-s6wQyC0PXTtpwXQEnDh_XZg_mWMU4HJ6jra3d20RSnayGFzhcBTvolmLdTQPc','android'),
(39,6,'customer','dYLJKodrTl6uHjghzCpZob:APA91bFlxuA6IB8SRHYPO9vH_6x9CONmfnK5_LbrhCnw_7TVzJSZlsBViOaqeJhruPKNe2_QhZVtHjyPzRSHHnUL95tMdhLbDmX_6AUVYxKEWKGLrq1zPsQ','android'),
(40,0,'customer','cxz6BP2HTFmYNBD2Bzw7Ul:APA91bGRcqqUVwe0ZgIsA_qXe6pCraM8i1W1Mjk8_bzYddEX0jb6ijINSwqHIxTQSax3QTt4MUHLzWib0lDHdSuSpEpKUf_7D0UkG0Qvc0OCl42PR9ED7VA','android'),
(42,0,'customer','dt4JbseNQCSBu2hECH6PS9:APA91bEFLemmKLsRIUKKeAq0eJnpil3MDVYlmapFeUAlZd6t5kYJZBSGm4UqicO3eTkve4PqoqePxVIucE5x_-_fAGPwBnEO5UPnkyVaSirssxgDEjO2fZg','android'),
(43,0,'customer','cNvxVQQmTJaPmwKcK0QZ-_:APA91bGk3Ds4NJ8ZJ0u2F9H1_nIEp_mx3pfVbcE2t8qqFRJRMlvOR1nYguDcyjz-j8c9dja4RG1JkungLqntiHjBGmrAgMpVHmnZKekzuwNkLlbJXuGvqpc','android'),
(45,3,'customer','dmWDPvrTSpiiOyJF80M1NC:APA91bH6gAZ5_WN2eql0KrvhTQsl6Iuz9tm2UOuo7HwBBSjXTFWouCFivFgrUBY9VPE5BQVMUTIFFq9V0RA4MFI9PkP2-n7wao3jqI5kD9fT2T3ZJJ86Blg','android'),
(46,3,'customer','cKFJ8pwXTxKPT3y_OiTnKM:APA91bHd997vr5F6CxseTlDbbzf5Ld3d9zuHhnLvYxayRZ0-TXw1LdNVJLktULnXAJUXX8SUn51XpyNDRzNgqoOd5N-t7Ng6iR01miuI7C7AGU3-Mi3Ok60','android'),
(47,1,'customer','cjZT-fwOQqCL0SKvsWEjgQ:APA91bG6dvMZAqTKEMxK-SbGQE4Anx0ws_oq8-MxO3AFJa4rRM_NBbvwthmjZFJK1RRxH9u4HrbL2U3J3kd9elQR8b9Ykxie7wT-9H8esx3KkJBlFkawd-8','android'),
(49,3,'customer','fKUI3owfTViq2tVLhuoXs_:APA91bHXzsFM__Cczbfi7JHHFOwX3cA0wOU0hWL9lGnDbqgeD_4Xa2UrGxtvYTWMmZv1qRj5Qs_OHa8sWO8MCBFf7wQ051zfw3fcXqLRV8Gp0KZcQwcESYg','android'),
(50,1,'customer','efIesxNCTHaec33BDmlZ4M:APA91bEzE9ua7o7VvsJGc4iDm55s8CfpMhgVPXBL1vlZzyIiNIbTqorvWdJx7Vc6JoywhrShMOnkkJHJtFnvusiM77fuy9_BuN-cgHGQ-4v7ZeqMdeOKmMM','android'),
(51,2,'customer','dKWQTP41QmCr0v9hf64DwI:APA91bEdbKaoxyVoG0O2eXAHmFl5a0hSOc29QWY8YVpjGBUu46tVrRStm9taCSy3fIeaEZSbZYOdBv1lDi6jjZvWYXBz8xI8FKiITD3DUTUbx8Zi9k1LN_4','android'),
(52,0,'customer','c80-UpZmQQmjU8EQfcUWoB:APA91bGGL8pK6qJ68upzRfYsOZDuoaQDmjf6Bbjc0oDSHd4gzUPDVtLycJDL1zYwOffBvfRpd_sYcsQjnbRpPWEqoZTkFcV1TPNhu4LFFrCzFLRpxOfJJno','android'),
(53,0,'customer','dhvFnt7IQbegrTflwMg0Ku:APA91bHX8koC1D8Yy-tdFRGGzIrZOaKtrUG1xkhueLECSrYhBlL4vDRcjB3DxTkjTrdA382SweHArsmEBCP4pbASNPWfgghQLK7OrRfuTQz7rB397uD1pkw','android'),
(54,0,'customer','c_aDI6UeQ5iVKYzw85WUa8:APA91bGSply1FvcTtuMbsiTy6V0cD17Ok08A0I42ov0udLWdwNQTTm2ZDM8NiJqDq304q7WrGocTIsYaAxgmr_sib_mGjvZBfyF_NfuVVB7oSgHzFovmUIc','android'),
(55,0,'customer','d_sWuTPcRO-AQZPpxtgwB-:APA91bFSjLY1UqQQeVkswLfLHCV0zcDlqOsiLxrQ7OoleqVv1d94TQWW8sa_iiiAGUiqNK4ixtYbNyzkdd0KyLpsNTyrhVmIJcf8mzmhVIwdM8rhicGACk4','android'),
(56,0,'customer','emn2eE7eSValeXynNsfj0r:APA91bEiEIDLVYcVwpdU2hV1281uIfz6Oxv4vV-0AIPohUYQUPnYLwJIHASrTsyZJ24Mc7P7nm-RpK608vVEAWgxYqUyVUedWm-9PDIDL9y_Rh_tN3y2eIc','android'),
(57,0,'customer','ddgKAf_HSKu46V_bQzvnbB:APA91bFNcCX6-huMrC8zg3Vm7VUioF-CNcaFaSh6yNPsTQz-ETh3LURz6zbrwP8H0mCkzAe0bC2IRJIVBK3kJtrH90oNRFw9XETqDW5aZGUoEbobU4do5Y4','android'),
(58,3,'customer','cKmNCUdXS8Cw--tWPN1SdR:APA91bG7VoE_LLHuI8nA7YQaOqh1EXJZBPpqHQdY5oDr-rPSl3RYotVKMFlmMSv9s12MmrdKTwxzZ9drwp0jYpvbo4dtgOu2aAbk_4zluRRkbK0xf-qnIfE','android'),
(59,3,'customer','etLftk8KSDq60eXphLt0Uu:APA91bEsc-BBIYZzsIMrhS28-Ahh7kp-7L5amS1r2oqgJpR0cIefRXEMEDAGzsQIZ4o0AaI3xsMd_OtIwo5x6MDggWkZBvjFiF0Y42TyPpEM0EJXUCc07vs','android'),
(60,1,'customer','dS_flb0OS7qjkYeEQs02Wu:APA91bGqtQq4rbnSUhAru1t-yZLbI-bu2zwb0CMn8AfZdrVPD3L0LDKcp8tsxuTg6dEmX5P6xtjMqonwtui_ljGIuYVArNEpGGRnp_Vx-kQHoAo2_xSMtRs','android'),
(61,0,'customer','faays8LSSsmpF9nAt5zbpU:APA91bG20aw7_OWMpHfXHoY5ZM9P4ARei1SKNAMU25tx2rS0uBi5odzkkRNDu8vyhlmScF-XPtc9kv2e-pQ_wApCoT1hGp4zm357Ls1SscjkMiyoFZbs38c','android'),
(62,0,'customer','fUQ_1GOsQNmjmw_ECeOmr-:APA91bH6QcJxekP01YR-IFiKVbGUH3XiFIqxSNPhQcU_aOOZotSsHSHRk9_JBxx7xDanm_BoHtVeP-eZ-H60DSjlNfSzYKcquZndz8Qg6TuAVUCRmuP4gdk','android'),
(64,0,'customer','dcc_SL06RiiNzj32P8GJgP:APA91bHB_9EChmMHJ7sk0YgePHrxr87MWKKYSrhi_Injt1EW3WlmRiriZVdZwsiS2LL1Ji8MUjC7CJVSMM1eWIQX4aU-G0SR-fI0KAQnHL7XzPAPcTAbeGA','android'),
(65,0,'customer','fAxMaIf2S-CTJQSXkRnwjQ:APA91bFW-dM11DEHN6evq7XTeUHvvgQcrOLzrjWPJQegyKI-IUWfCsveuAnbcT6QtgJ2Fqul41hhoDDHeYB2w83JlOYLn9QIF8ywzlnTCVFV47hgzuwtX24','android'),
(66,0,'customer','fF77kR2fRmy0T16dVG-2-t:APA91bGK-TjjTd2FRBpMWXWnSQlhCCrcTxTiL92UkWuL6z4emL6CHKoG3L5fKY63qEUYsTcF46zDCj0Tvbvr7JLkoKbyHFXbkLW1skvL_4p6gJz_3zw6VQY','android'),
(67,1,'customer','dnjvWm-VT6uLbindNAmfQI:APA91bGA6YrGJyuR05F0Uu9V72n1Up6k5pawU_HHZgflhz--nsmV7pv3LEtRMQlEKtIB-NxXak9O9Lszvq7YwqQUdKAILpS39GHPIsClw_5H_PrAhrtGXhc','android'),
(68,0,'customer','d3j5O4t_TyOiH-iECdsAnX:APA91bFVy8kkBUru88G2cfGqn7DuY-79R6J-U1nFDHfZa7uGPnutAaJS6zloCGgA7bq0Yh_LS6iju7qNrlhiUQLrOaDWdzqP-XLiyWHKF-Dn-ysUZqR5ZBw','android'),
(69,2,'customer','cKVKTzRbSBiXTjSQC--_KH:APA91bGlIsGeBXhvcIG_n2qbNnr43dEe5p6mV6RJFxjdS-G80h20lzNcBTgnOmHYri_-ZHJhqp_kBGUIRcBCpBj6G9YEfbU5LVURlHlNgT-vlmHQGm8Gpcw','android'),
(70,1,'customer','cO5M2VKmRcW9eFLeaxb3UX:APA91bGwCySelROFYTy_bQp7eIIQkT33buTjAhLpCN1i5zA1WqRf8lurs3QJXKvyXiCMEflP41WleAUhFVZe8chDTwD9beioLkFEFQTg3xB7yppnJxosfkY','android'),
(71,0,'customer','fRxnTnDXQziWZAyGzDac71:APA91bHBDULmM7vYfTb-vhr_uwAS8F0LwhPGdw6aerualpq4pjlFhQ9uCeO3sJftEK1o4no_1I_oMPxvyry-3IvFIF5za0u91rRPjlnJkmJo3FHKYWXZhf8','android'),
(72,14,'customer','eziJSTM2ToC2QsueBTatV-:APA91bEOAXlA74MdqQ102C9YwZY_-u1n-etokyDfFzSbeQPhngbOhgtd8FMks9PH7jSec5Mk6zHT7WxPNJNhwWJhwTZpsqIjl7eFVp7Z0MyixYO9JbMhZ58','android'),
(73,15,'customer','dMXZrwemQc6JZ0ZIQ9rHEi:APA91bGLXoHyD_P8_vS5ReuLc55-oLW-KW64mbfh7TLbkxFAAddhile4qvF7V1w8_-0GDkqRfysbEQo3ZWCaM0cU4fdKp_2XQlRcZoDz1H_PMcRafEcn1SU','android'),
(74,2,'customer','frwzX4IlSImlp1hhISTIaM:APA91bE6dplQqN169-dQYRfzdN4wl0HdokCI4SDh5iW8flFdCVD_MCH1f1gI8EieUVqQucWN-_ROBecKaLyLgNtPCaKMlFfJgyJmDAJFysG8eYgBmDdake4','android'),
(75,1,'customer','dh8dJKKBQNWyTh0b4JG5yX:APA91bEAHGfF9u0Ak7hRbrWA9c2jqNGA8Gg2p9Z4QuD7pVWDMm9TBnrdxDam04fMtcAYkRFse2KgVWSHah5YD4PliPKUU9S0fnO1IMtDhe_wrEm9DeQXhfo','android'),
(76,16,'customer','cBgHUM1DSmic_YpQTXHa4B:APA91bHLMw6bd5Cnopl2644Dr7Nm2UOVjPz5jIZokPeu2vYYgA_Rftob8nkWtixc9JYRU7hk4pAQT1y1PKmzhDnb8bIBZTc7gUJ8ZKlb6mIy2Y-dnnTf480','android'),
(77,16,'customer','cBgHUM1DSmic_YpQTXHa4B:APA91bHLMw6bd5Cnopl2644Dr7Nm2UOVjPz5jIZokPeu2vYYgA_Rftob8nkWtixc9JYRU7hk4pAQT1y1PKmzhDnb8bIBZTc7gUJ8ZKlb6mIy2Y-dnnTf480','android'),
(78,17,'customer','c3ApqzCuR2it__1ijG31wO:APA91bHLx87oigoYNlzqo57V6ZzckB1G7gVB0QvhNIWZKV-WZ9t6k0yGRYLOHeH1V1ve5UdoC6otNrTzwkYfub0r4Kvt7NV6Dv3XHjFiTeacsvZl3speAPw','android'),
(79,18,'customer','foDqBe2ST5WC0UkJPGbefq:APA91bHMy4PlkukjQ_qg5yjSQuFrD3WZwxx5o8t5XGdVhESbZGJusrq46fg79UalNAAK2HKtYlTehnwqcG9h6LkOxqX4v0fMoxtcmsXPGns6kUgDdSHbXhg','android');
/*!40000 ALTER TABLE `user_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) DEFAULT NULL,
  `email` varchar(191) DEFAULT NULL,
  `password` varchar(191) DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT 0,
  `email_verification_code` varchar(191) DEFAULT NULL,
  `profile` varchar(191) DEFAULT NULL,
  `country_code` varchar(191) NOT NULL DEFAULT '91',
  `mobile` varchar(191) NOT NULL,
  `balance` double NOT NULL DEFAULT 0,
  `referral_code` varchar(191) DEFAULT NULL,
  `friends_code` varchar(191) DEFAULT NULL,
  `subscription_id` int(11) DEFAULT NULL,
  `subscription_start_date` date DEFAULT NULL,
  `subscription_end_date` date DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `type` enum('email','google','apple','phone') NOT NULL DEFAULT 'phone',
  `stripe_id` varchar(191) DEFAULT NULL,
  `pm_type` varchar(191) DEFAULT NULL,
  `pm_last_four` varchar(4) DEFAULT NULL,
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `users_stripe_id_index` (`stripe_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES
(1,'Hurair','<EMAIL>',NULL,0,NULL,'','+91','9544794516',0,'2B7B4D',NULL,1,'2025-07-27','2025-08-27',1,'2025-06-26 13:58:03','2025-06-29 15:37:03',NULL,'phone',NULL,NULL,NULL,NULL),
(2,'maaz','<EMAIL>',NULL,0,NULL,'','+91','9995286658',50,'9218C7',NULL,1,'2025-07-12','2025-07-27',1,'2025-06-26 19:43:20','2025-06-27 17:18:53',NULL,'phone',NULL,NULL,NULL,NULL),
(3,'bhowmy','<EMAIL>',NULL,0,NULL,'','+91','9495037815',0,'07588D',NULL,1,'2025-07-13','2025-08-13',1,'2025-06-27 20:26:24','2025-06-27 20:26:24',NULL,'phone',NULL,NULL,NULL,NULL),
(4,'Sameer Vellamunda','<EMAIL>',NULL,0,NULL,'','+91','9544928362',0,'2B1F73',NULL,NULL,'0000-00-00','0000-00-00',1,'2025-07-04 12:56:15','2025-07-04 12:56:15',NULL,'phone',NULL,NULL,NULL,NULL),
(5,'Anaswara K Pillai','<EMAIL>',NULL,0,NULL,'','+91','9207003168',0,'A89F5F',NULL,NULL,'0000-00-00','0000-00-00',1,'2025-07-07 01:49:45','2025-07-07 01:49:45',NULL,'phone',NULL,NULL,NULL,NULL),
(6,'Rupesh R Menon','<EMAIL>',NULL,0,NULL,'','+91','7306276083',0,'2C069B',NULL,NULL,'0000-00-00','0000-00-00',1,'2025-07-09 09:59:40','2025-07-09 09:59:40',NULL,'phone',NULL,NULL,NULL,NULL),
(7,'shijin','<EMAIL>','$2y$10$8Xie7Nle3zIMZFNS4sboYuGhfKILjvjskEN6uZeEY7QaA2NLnUH7q',0,NULL,'','','9961723100',0,'7274B4',NULL,1,'2025-07-11','2025-08-11',1,'2025-07-10 21:23:40','2025-07-10 21:23:40',NULL,'phone',NULL,NULL,NULL,NULL),
(13,'Burhan','<EMAIL>',NULL,0,NULL,'','+91','7012836907',0,'968CDC',NULL,NULL,NULL,NULL,1,'2025-07-22 20:12:56','2025-07-22 20:12:56',NULL,'phone',NULL,NULL,NULL,NULL),
(14,'zoom','<EMAIL>',NULL,0,NULL,'','+91','9747444006',0,'7E47FE',NULL,NULL,NULL,NULL,1,'2025-07-23 18:33:14','2025-07-23 18:33:14',NULL,'phone',NULL,NULL,NULL,NULL),
(15,'habeeb','<EMAIL>',NULL,0,NULL,'','+91','9747031816',0,'BDE0DB',NULL,NULL,NULL,NULL,1,'2025-07-25 11:19:34','2025-07-25 11:19:34',NULL,'phone',NULL,NULL,NULL,NULL),
(16,'Vijayakrishnan','<EMAIL>',NULL,0,NULL,'','+91','9447376195',0,'1A3174',NULL,NULL,NULL,NULL,1,'2025-07-26 16:25:01','2025-07-26 16:25:01',NULL,'phone',NULL,NULL,NULL,NULL),
(17,'varadha k r','<EMAIL>',NULL,0,NULL,'','+91','9804400600',0,'7A7C5A',NULL,NULL,NULL,NULL,1,'2025-07-29 16:31:02','2025-07-29 16:31:02',NULL,'phone',NULL,NULL,NULL,NULL),
(18,'santhosh','<EMAIL>',NULL,0,NULL,'','+91','9652535893',0,'9A3DC0',NULL,NULL,NULL,NULL,1,'2025-07-29 19:31:32','2025-07-29 19:31:32',NULL,'phone',NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wallet_transactions`
--

DROP TABLE IF EXISTS `wallet_transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wallet_transactions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL,
  `order_item_id` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `type` varchar(191) NOT NULL,
  `amount` double NOT NULL,
  `txn_id` varchar(191) DEFAULT NULL,
  `payment_type` varchar(191) DEFAULT NULL,
  `transaction_date` datetime NOT NULL DEFAULT '2025-06-20 14:17:32',
  `message` varchar(191) NOT NULL,
  `status` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wallet_transactions`
--

LOCK TABLES `wallet_transactions` WRITE;
/*!40000 ALTER TABLE `wallet_transactions` DISABLE KEYS */;
INSERT INTO `wallet_transactions` VALUES
(1,NULL,NULL,2,'credit',50,'admin','admin','2025-06-20 14:17:32','Zoom coin','1','2025-06-27 17:18:53','2025-06-27 17:18:53'),
(2,NULL,NULL,1,'credit',50,'admin','admin','2025-06-20 14:17:32','Zoomcoin','1','2025-06-29 15:34:29','2025-06-29 15:34:29'),
(3,11,0,1,'debit',50,NULL,NULL,'2025-06-20 14:17:32','Used against Order Placement','1','2025-06-29 15:37:03','2025-06-29 15:37:03');
/*!40000 ALTER TABLE `wallet_transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `withdrawal_requests`
--

DROP TABLE IF EXISTS `withdrawal_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `withdrawal_requests` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `type` varchar(191) NOT NULL COMMENT 'user, seller, delivery_boy',
  `type_id` int(11) NOT NULL,
  `amount` double NOT NULL,
  `message` text NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 0,
  `remark` text DEFAULT NULL COMMENT 'This is store reject request',
  `receipt_image` varchar(191) DEFAULT NULL COMMENT 'If status: approved (1) then upload receipt as proof',
  `device_type` varchar(191) DEFAULT NULL COMMENT '0 => Web, 1 => Android, 2 => IOS',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `withdrawal_requests`
--

LOCK TABLES `withdrawal_requests` WRITE;
/*!40000 ALTER TABLE `withdrawal_requests` DISABLE KEYS */;
/*!40000 ALTER TABLE `withdrawal_requests` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-01  5:23:17
