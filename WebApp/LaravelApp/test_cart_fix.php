<?php

require_once __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Helpers\CommonHelper;

echo "=== Testing Cart Calculation Fix with Production Data ===\n\n";

// Test Case: 0.5 kg of Chicken Mandi Cut (variant_id 41)
$variant_ids = [41];
$quantities = [0.5];

echo "Testing: 0.5 kg of Chicken Mandi Cut (variant_id 41)\n";
echo "Expected: ₹120 (0.5kg = 500gm = base quantity)\n\n";

try {
    $result = CommonHelper::calculateTotalAmount($variant_ids, $quantities);
    
    echo "Results:\n";
    echo "- Total Amount: ₹" . $result['total_amount'] . "\n";
    echo "- Save Price: ₹" . $result['save_price'] . "\n\n";
    
    $expected = 120;
    $actual = $result['total_amount'];
    
    echo "Expected: ₹$expected\n";
    echo "Actual: ₹$actual\n";
    echo "Status: " . ($actual == $expected ? "✅ PASS" : "❌ FAIL") . "\n\n";
    
    if ($actual == $expected) {
        echo "🎉 SUCCESS: Cart calculation fix is working correctly with production data!\n";
        echo "Ready for production deployment.\n";
    } else {
        echo "❌ FAILURE: Cart calculation fix needs more work.\n";
        echo "Expected ₹$expected but got ₹$actual\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
