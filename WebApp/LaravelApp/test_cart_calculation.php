<?php

// Simple test script to verify cart calculation fix
// This simulates the calculation logic to test our fix

function calculateTotalAmount($variant_ids, $quantityArray) {
    $total_amount = 0;
    
    if(count($variant_ids) === count($quantityArray)){
        foreach ($variant_ids as $key => $variant_id){
            
            // Simulate variant data for product_variant_id 41 (Chicken Mandi Cut)
            $variant = (object)[
                'measurement' => 500,  // 500gm
                'price' => 120,        // ₹120
                'discounted_price' => 0
            ];
            
            $mainPrice = ($variant->discounted_price != 0 && $variant->discounted_price != "") 
                ? $variant->discounted_price 
                : $variant->price;

            // Calculate price based on base measurement ratio
            // Formula: basePrice * (requestedQuantity / baseMeasurement)
            $baseMeasurement = floatval($variant->measurement);
            $requestedQuantity = floatval($quantityArray[$key]);

            // Prevent division by zero
            if ($baseMeasurement <= 0) {
                $baseMeasurement = 1.0;
            }

            // Handle unit conversion: if baseMeasurement is in grams (>=500) and requestedQuantity appears to be in kg (<10)
            // Convert kg to grams for proper calculation
            // Example: 0.5 kg should become 500 gm to match baseMeasurement of 500 gm
            if ($baseMeasurement >= 500 && $requestedQuantity < 10 && $requestedQuantity > 0) {
                $requestedQuantityInGrams = $requestedQuantity * 1000; // Convert kg to grams
                $quantityRatio = $requestedQuantityInGrams / $baseMeasurement;
                echo "Converted Requested Quantity: $requestedQuantityInGrams gm\n";
            } else {
                // For other cases, use direct ratio
                $quantityRatio = $requestedQuantity / $baseMeasurement;
                echo "Converted Requested Quantity: $requestedQuantity (no conversion)\n";
            }
            $price = floatval($mainPrice) * $quantityRatio;
            $total_amount += floatval($price);
            
            echo "Variant ID: $variant_id\n";
            echo "Base Measurement: $baseMeasurement gm\n";
            echo "Original Requested Quantity: {$quantityArray[$key]}\n";
            echo "Converted Requested Quantity: $requestedQuantity gm\n";
            echo "Quantity Ratio: $quantityRatio\n";
            echo "Main Price: ₹$mainPrice\n";
            echo "Calculated Price: ₹$price\n";
            echo "---\n";
        }
    }
    
    return $total_amount;
}

// Test cases
echo "=== Cart Calculation Test ===\n\n";

// Test Case 1: Original issue - 0.5 kg of Chicken Mandi Cut
echo "Test Case 1: 0.5 kg of Chicken Mandi Cut (₹120 for 500gm)\n";
$result1 = calculateTotalAmount([41], [0.5]);
echo "Expected: ₹120 (0.5kg = 500gm = base quantity, so full base price)\n";
echo "Actual: ₹$result1\n";
echo "Status: " . ($result1 == 120 ? "✅ PASS" : "❌ FAIL") . "\n\n";

// Test Case 2: 1.0 kg (should be double the price)
echo "Test Case 2: 1.0 kg of Chicken Mandi Cut\n";
$result2 = calculateTotalAmount([41], [1.0]);
echo "Expected: ₹240 (1.0kg = 1000gm = 2x base quantity, so double price)\n";
echo "Actual: ₹$result2\n";
echo "Status: " . ($result2 == 240 ? "✅ PASS" : "❌ FAIL") . "\n\n";

// Test Case 3: 0.25 kg (should be half the price)
echo "Test Case 3: 0.25 kg of Chicken Mandi Cut\n";
$result3 = calculateTotalAmount([41], [0.25]);
echo "Expected: ₹60 (0.25kg = 250gm = 0.5x base quantity, so half price)\n";
echo "Actual: ₹$result3\n";
echo "Status: " . ($result3 == 60 ? "✅ PASS" : "❌ FAIL") . "\n\n";

// Test Case 4: 1.5 kg
echo "Test Case 4: 1.5 kg of Chicken Mandi Cut\n";
$result4 = calculateTotalAmount([41], [1.5]);
echo "Expected: ₹360 (1.5kg = 1500gm = 3x base quantity, so triple price)\n";
echo "Actual: ₹$result4\n";
echo "Status: " . ($result4 == 360 ? "✅ PASS" : "❌ FAIL") . "\n\n";

echo "=== Test Summary ===\n";
$passed = 0;
$total = 4;

if ($result1 == 120) $passed++;
if ($result2 == 240) $passed++;
if ($result3 == 60) $passed++;
if ($result4 == 360) $passed++;

echo "Passed: $passed/$total tests\n";
echo "Status: " . ($passed == $total ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED") . "\n";

?>
